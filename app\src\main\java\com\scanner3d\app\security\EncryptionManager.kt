package com.scanner3d.app.security

import android.content.Context
import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import android.util.Log
import androidx.biometric.BiometricManager
import androidx.biometric.BiometricPrompt
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.security.KeyStore
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.GCMParameterSpec
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

class EncryptionManager(private val context: Context) {
    
    companion object {
        private const val TAG = "EncryptionManager"
        private const val ANDROID_KEYSTORE = "AndroidKeyStore"
        private const val KEY_ALIAS_DATA = "Scanner3D_DataKey"
        private const val KEY_ALIAS_AUTH = "Scanner3D_AuthKey"
        private const val TRANSFORMATION = "AES/GCM/NoPadding"
        private const val GCM_IV_LENGTH = 12
        private const val GCM_TAG_LENGTH = 16
    }
    
    private val keyStore: KeyStore = KeyStore.getInstance(ANDROID_KEYSTORE).apply { load(null) }
    
    enum class AuthenticationMethod {
        BIOMETRIC,
        PIN,
        PATTERN,
        PASSWORD
    }
    
    data class EncryptionResult(
        val encryptedData: ByteArray,
        val iv: ByteArray,
        val authTag: ByteArray? = null
    )
    
    data class AuthenticationResult(
        val isSuccess: Boolean,
        val method: AuthenticationMethod,
        val errorMessage: String? = null
    )
    
    init {
        initializeKeys()
    }
    
    suspend fun encryptData(data: ByteArray, useAuthKey: Boolean = false): EncryptionResult = withContext(Dispatchers.Default) {
        try {
            val keyAlias = if (useAuthKey) KEY_ALIAS_AUTH else KEY_ALIAS_DATA
            val secretKey = getOrCreateSecretKey(keyAlias, useAuthKey)
            
            val cipher = Cipher.getInstance(TRANSFORMATION)
            cipher.init(Cipher.ENCRYPT_MODE, secretKey)
            
            val iv = cipher.iv
            val encryptedData = cipher.doFinal(data)
            
            Log.d(TAG, "Data encrypted successfully (${data.size} -> ${encryptedData.size} bytes)")
            
            EncryptionResult(
                encryptedData = encryptedData,
                iv = iv
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Encryption failed", e)
            throw EncryptionException("Failed to encrypt data: ${e.message}", e)
        }
    }
    
    suspend fun decryptData(
        encryptedData: ByteArray,
        iv: ByteArray,
        useAuthKey: Boolean = false
    ): ByteArray = withContext(Dispatchers.Default) {
        try {
            val keyAlias = if (useAuthKey) KEY_ALIAS_AUTH else KEY_ALIAS_DATA
            val secretKey = getSecretKey(keyAlias)
                ?: throw EncryptionException("Decryption key not found")
            
            val cipher = Cipher.getInstance(TRANSFORMATION)
            val spec = GCMParameterSpec(GCM_TAG_LENGTH * 8, iv)
            cipher.init(Cipher.DECRYPT_MODE, secretKey, spec)
            
            val decryptedData = cipher.doFinal(encryptedData)
            
            Log.d(TAG, "Data decrypted successfully (${encryptedData.size} -> ${decryptedData.size} bytes)")
            decryptedData
            
        } catch (e: Exception) {
            Log.e(TAG, "Decryption failed", e)
            throw EncryptionException("Failed to decrypt data: ${e.message}", e)
        }
    }
    
    suspend fun encryptFile(filePath: String, outputPath: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val inputFile = java.io.File(filePath)
            val outputFile = java.io.File(outputPath)
            
            if (!inputFile.exists()) {
                throw EncryptionException("Input file not found: $filePath")
            }
            
            val data = inputFile.readBytes()
            val encryptionResult = encryptData(data)
            
            // Write encrypted data with metadata
            outputFile.outputStream().use { output ->
                // Write IV length and IV
                output.write(encryptionResult.iv.size)
                output.write(encryptionResult.iv)
                
                // Write encrypted data
                output.write(encryptionResult.encryptedData)
            }
            
            Log.d(TAG, "File encrypted: $filePath -> $outputPath")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "File encryption failed", e)
            false
        }
    }
    
    suspend fun decryptFile(encryptedPath: String, outputPath: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val encryptedFile = java.io.File(encryptedPath)
            val outputFile = java.io.File(outputPath)
            
            if (!encryptedFile.exists()) {
                throw EncryptionException("Encrypted file not found: $encryptedPath")
            }
            
            encryptedFile.inputStream().use { input ->
                // Read IV
                val ivLength = input.read()
                val iv = ByteArray(ivLength)
                input.read(iv)
                
                // Read encrypted data
                val encryptedData = input.readBytes()
                
                // Decrypt data
                val decryptedData = decryptData(encryptedData, iv)
                
                // Write decrypted data
                outputFile.writeBytes(decryptedData)
            }
            
            Log.d(TAG, "File decrypted: $encryptedPath -> $outputPath")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "File decryption failed", e)
            false
        }
    }
    
    fun isBiometricAvailable(): Boolean {
        val biometricManager = BiometricManager.from(context)
        return when (biometricManager.canAuthenticate(BiometricManager.Authenticators.BIOMETRIC_STRONG)) {
            BiometricManager.BIOMETRIC_SUCCESS -> true
            else -> false
        }
    }
    
    suspend fun authenticateWithBiometric(
        activity: FragmentActivity,
        title: String = "Biometric Authentication",
        subtitle: String = "Use your fingerprint or face to authenticate",
        negativeButtonText: String = "Cancel"
    ): AuthenticationResult = suspendCoroutine { continuation ->
        
        if (!isBiometricAvailable()) {
            continuation.resume(
                AuthenticationResult(
                    isSuccess = false,
                    method = AuthenticationMethod.BIOMETRIC,
                    errorMessage = "Biometric authentication not available"
                )
            )
            return@suspendCoroutine
        }
        
        val executor = ContextCompat.getMainExecutor(context)
        val biometricPrompt = BiometricPrompt(activity, executor, object : BiometricPrompt.AuthenticationCallback() {
            override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
                super.onAuthenticationError(errorCode, errString)
                Log.e(TAG, "Biometric authentication error: $errString")
                continuation.resume(
                    AuthenticationResult(
                        isSuccess = false,
                        method = AuthenticationMethod.BIOMETRIC,
                        errorMessage = errString.toString()
                    )
                )
            }
            
            override fun onAuthenticationSucceeded(result: BiometricPrompt.AuthenticationResult) {
                super.onAuthenticationSucceeded(result)
                Log.d(TAG, "Biometric authentication succeeded")
                continuation.resume(
                    AuthenticationResult(
                        isSuccess = true,
                        method = AuthenticationMethod.BIOMETRIC
                    )
                )
            }
            
            override fun onAuthenticationFailed() {
                super.onAuthenticationFailed()
                Log.w(TAG, "Biometric authentication failed")
                continuation.resume(
                    AuthenticationResult(
                        isSuccess = false,
                        method = AuthenticationMethod.BIOMETRIC,
                        errorMessage = "Authentication failed"
                    )
                )
            }
        })
        
        val promptInfo = BiometricPrompt.PromptInfo.Builder()
            .setTitle(title)
            .setSubtitle(subtitle)
            .setNegativeButtonText(negativeButtonText)
            .build()
        
        biometricPrompt.authenticate(promptInfo)
    }
    
    fun validatePIN(pin: String, storedPINHash: String): Boolean {
        return try {
            val hashedInput = hashPIN(pin)
            hashedInput == storedPINHash
        } catch (e: Exception) {
            Log.e(TAG, "PIN validation failed", e)
            false
        }
    }
    
    fun hashPIN(pin: String): String {
        return try {
            val digest = java.security.MessageDigest.getInstance("SHA-256")
            val salt = "Scanner3D_Salt_2024" // In production, use random salt per user
            val saltedPIN = pin + salt
            val hashedBytes = digest.digest(saltedPIN.toByteArray())
            hashedBytes.joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            Log.e(TAG, "PIN hashing failed", e)
            throw EncryptionException("Failed to hash PIN: ${e.message}", e)
        }
    }
    
    fun generateSecureToken(length: Int = 32): String {
        val secureRandom = java.security.SecureRandom()
        val bytes = ByteArray(length)
        secureRandom.nextBytes(bytes)
        return bytes.joinToString("") { "%02x".format(it) }
    }
    
    fun clearKeys() {
        try {
            if (keyStore.containsAlias(KEY_ALIAS_DATA)) {
                keyStore.deleteEntry(KEY_ALIAS_DATA)
                Log.d(TAG, "Data key cleared")
            }
            
            if (keyStore.containsAlias(KEY_ALIAS_AUTH)) {
                keyStore.deleteEntry(KEY_ALIAS_AUTH)
                Log.d(TAG, "Auth key cleared")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear keys", e)
        }
    }
    
    private fun initializeKeys() {
        try {
            // Initialize data encryption key
            getOrCreateSecretKey(KEY_ALIAS_DATA, requireAuth = false)
            
            // Initialize authentication key (requires biometric/PIN)
            if (isBiometricAvailable()) {
                getOrCreateSecretKey(KEY_ALIAS_AUTH, requireAuth = true)
            }
            
            Log.d(TAG, "Encryption keys initialized")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize keys", e)
        }
    }
    
    private fun getOrCreateSecretKey(keyAlias: String, requireAuth: Boolean): SecretKey {
        return getSecretKey(keyAlias) ?: createSecretKey(keyAlias, requireAuth)
    }
    
    private fun getSecretKey(keyAlias: String): SecretKey? {
        return try {
            if (keyStore.containsAlias(keyAlias)) {
                keyStore.getKey(keyAlias, null) as SecretKey
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get secret key: $keyAlias", e)
            null
        }
    }
    
    private fun createSecretKey(keyAlias: String, requireAuth: Boolean): SecretKey {
        val keyGenerator = KeyGenerator.getInstance(KeyProperties.KEY_ALGORITHM_AES, ANDROID_KEYSTORE)
        
        val keyGenParameterSpec = KeyGenParameterSpec.Builder(
            keyAlias,
            KeyProperties.PURPOSE_ENCRYPT or KeyProperties.PURPOSE_DECRYPT
        )
            .setBlockModes(KeyProperties.BLOCK_MODE_GCM)
            .setEncryptionPaddings(KeyProperties.ENCRYPTION_PADDING_NONE)
            .setKeySize(256)
            .apply {
                if (requireAuth && isBiometricAvailable()) {
                    setUserAuthenticationRequired(true)
                    setUserAuthenticationParameters(
                        30, // 30 seconds timeout
                        KeyProperties.AUTH_BIOMETRIC_STRONG or KeyProperties.AUTH_DEVICE_CREDENTIAL
                    )
                }
            }
            .build()
        
        keyGenerator.init(keyGenParameterSpec)
        val secretKey = keyGenerator.generateKey()
        
        Log.d(TAG, "Created secret key: $keyAlias (requireAuth: $requireAuth)")
        return secretKey
    }
    
    class EncryptionException(message: String, cause: Throwable? = null) : Exception(message, cause)
}

