package com.scanner3d.app.ui.gallery

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.scanner3d.app.R
import com.scanner3d.app.databinding.ActivityGalleryBinding
import com.scanner3d.app.ui.model.ModelViewerActivity
import com.scanner3d.app.ui.scanning.ScanningActivity
import com.scanner3d.app.viewmodel.GalleryViewModel

class GalleryActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityGalleryBinding
    private val viewModel: GalleryViewModel by viewModels()
    private lateinit var galleryAdapter: GalleryAdapter
    
    private var isGridView = true
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityGalleryBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupUI()
        setupRecyclerView()
        observeViewModel()
        
        // Load scans
        viewModel.loadScans()
    }
    
    private fun setupUI() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = getString(R.string.gallery_title)
        
        binding.apply {
            // FAB for new scan
            fabNewScan.setOnClickListener {
                startActivity(Intent(this@GalleryActivity, ScanningActivity::class.java))
            }
            
            // Filter buttons
            btnFilterAll.setOnClickListener {
                viewModel.setFilter(GalleryViewModel.FilterType.ALL)
                updateFilterButtons(GalleryViewModel.FilterType.ALL)
            }
            
            btnFilterRecent.setOnClickListener {
                viewModel.setFilter(GalleryViewModel.FilterType.RECENT)
                updateFilterButtons(GalleryViewModel.FilterType.RECENT)
            }
            
            btnFilterFavorites.setOnClickListener {
                viewModel.setFilter(GalleryViewModel.FilterType.FAVORITES)
                updateFilterButtons(GalleryViewModel.FilterType.FAVORITES)
            }
            
            // Sort options
            btnSortDate.setOnClickListener {
                viewModel.setSortOrder(GalleryViewModel.SortOrder.DATE_DESC)
                updateSortButtons(GalleryViewModel.SortOrder.DATE_DESC)
            }
            
            btnSortName.setOnClickListener {
                viewModel.setSortOrder(GalleryViewModel.SortOrder.NAME_ASC)
                updateSortButtons(GalleryViewModel.SortOrder.NAME_ASC)
            }
            
            btnSortSize.setOnClickListener {
                viewModel.setSortOrder(GalleryViewModel.SortOrder.SIZE_DESC)
                updateSortButtons(GalleryViewModel.SortOrder.SIZE_DESC)
            }
            
            // Search
            searchView.setOnQueryTextListener(object : androidx.appcompat.widget.SearchView.OnQueryTextListener {
                override fun onQueryTextSubmit(query: String?): Boolean {
                    query?.let { viewModel.searchScans(it) }
                    return true
                }
                
                override fun onQueryTextChange(newText: String?): Boolean {
                    newText?.let { viewModel.searchScans(it) }
                    return true
                }
            })
        }
        
        // Initialize filter state
        updateFilterButtons(GalleryViewModel.FilterType.ALL)
        updateSortButtons(GalleryViewModel.SortOrder.DATE_DESC)
    }
    
    private fun setupRecyclerView() {
        galleryAdapter = GalleryAdapter(
            onItemClick = { scanItem ->
                openModelViewer(scanItem)
            },
            onItemLongClick = { scanItem ->
                showScanOptions(scanItem)
            },
            onFavoriteClick = { scanItem ->
                viewModel.toggleFavorite(scanItem.id)
            },
            onShareClick = { scanItem ->
                shareScan(scanItem)
            }
        )
        
        binding.recyclerViewScans.apply {
            adapter = galleryAdapter
            updateLayoutManager()
        }
    }
    
    private fun updateLayoutManager() {
        binding.recyclerViewScans.layoutManager = if (isGridView) {
            GridLayoutManager(this, 2)
        } else {
            LinearLayoutManager(this)
        }
        galleryAdapter.setViewType(if (isGridView) GalleryAdapter.ViewType.GRID else GalleryAdapter.ViewType.LIST)
    }
    
    private fun observeViewModel() {
        viewModel.scanItems.observe(this, Observer { scans ->
            galleryAdapter.submitList(scans)
            updateEmptyState(scans.isEmpty())
        })
        
        viewModel.isLoading.observe(this, Observer { isLoading ->
            binding.progressBar.visibility = if (isLoading) android.view.View.VISIBLE else android.view.View.GONE
        })
        
        viewModel.errorMessage.observe(this, Observer { error ->
            error?.let {
                Toast.makeText(this, it, Toast.LENGTH_LONG).show()
                viewModel.clearError()
            }
        })
        
        viewModel.scanCount.observe(this, Observer { count ->
            supportActionBar?.subtitle = "$count scans"
        })
    }
    
    private fun updateEmptyState(isEmpty: Boolean) {
        binding.apply {
            if (isEmpty) {
                recyclerViewScans.visibility = android.view.View.GONE
                llEmptyState.visibility = android.view.View.VISIBLE
            } else {
                recyclerViewScans.visibility = android.view.View.VISIBLE
                llEmptyState.visibility = android.view.View.GONE
            }
        }
    }
    
    private fun updateFilterButtons(activeFilter: GalleryViewModel.FilterType) {
        binding.apply {
            btnFilterAll.isSelected = activeFilter == GalleryViewModel.FilterType.ALL
            btnFilterRecent.isSelected = activeFilter == GalleryViewModel.FilterType.RECENT
            btnFilterFavorites.isSelected = activeFilter == GalleryViewModel.FilterType.FAVORITES
        }
    }
    
    private fun updateSortButtons(activeSortOrder: GalleryViewModel.SortOrder) {
        binding.apply {
            btnSortDate.isSelected = activeSortOrder == GalleryViewModel.SortOrder.DATE_DESC
            btnSortName.isSelected = activeSortOrder == GalleryViewModel.SortOrder.NAME_ASC
            btnSortSize.isSelected = activeSortOrder == GalleryViewModel.SortOrder.SIZE_DESC
        }
    }
    
    private fun openModelViewer(scanItem: com.scanner3d.app.data.model.ScanItem) {
        val intent = Intent(this, ModelViewerActivity::class.java).apply {
            putExtra(ModelViewerActivity.EXTRA_SCAN_ID, scanItem.id)
            putExtra(ModelViewerActivity.EXTRA_MODEL_PATH, scanItem.filePath)
        }
        startActivity(intent)
    }
    
    private fun showScanOptions(scanItem: com.scanner3d.app.data.model.ScanItem) {
        val options = arrayOf(
            "Open",
            "Rename",
            "Share",
            "Export",
            "Delete"
        )
        
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(scanItem.name)
            .setItems(options) { _, which ->
                when (which) {
                    0 -> openModelViewer(scanItem)
                    1 -> showRenameDialog(scanItem)
                    2 -> shareScan(scanItem)
                    3 -> exportScan(scanItem)
                    4 -> showDeleteConfirmation(scanItem)
                }
            }
            .show()
    }
    
    private fun showRenameDialog(scanItem: com.scanner3d.app.data.model.ScanItem) {
        val editText = android.widget.EditText(this).apply {
            setText(scanItem.name)
            selectAll()
        }
        
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Rename Scan")
            .setView(editText)
            .setPositiveButton("Rename") { _, _ ->
                val newName = editText.text.toString().trim()
                if (newName.isNotEmpty()) {
                    viewModel.renameScan(scanItem.id, newName)
                }
            }
            .setNegativeButton("Cancel", null)
            .show()
    }
    
    private fun shareScan(scanItem: com.scanner3d.app.data.model.ScanItem) {
        val shareIntent = Intent().apply {
            action = Intent.ACTION_SEND
            type = "application/octet-stream"
            putExtra(Intent.EXTRA_STREAM, android.net.Uri.fromFile(java.io.File(scanItem.filePath)))
            putExtra(Intent.EXTRA_TEXT, "Check out this 3D scan: ${scanItem.name}")
        }
        startActivity(Intent.createChooser(shareIntent, "Share 3D Scan"))
    }
    
    private fun exportScan(scanItem: com.scanner3d.app.data.model.ScanItem) {
        // TODO: Implement export functionality
        Toast.makeText(this, "Export functionality coming soon", Toast.LENGTH_SHORT).show()
    }
    
    private fun showDeleteConfirmation(scanItem: com.scanner3d.app.data.model.ScanItem) {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Delete Scan")
            .setMessage("Are you sure you want to delete \"${scanItem.name}\"? This action cannot be undone.")
            .setPositiveButton("Delete") { _, _ ->
                viewModel.deleteScan(scanItem.id)
                Toast.makeText(this, "Scan deleted", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("Cancel", null)
            .show()
    }
    
    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_gallery, menu)
        return true
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            R.id.action_view_toggle -> {
                isGridView = !isGridView
                updateLayoutManager()
                item.setIcon(if (isGridView) R.drawable.ic_view_list else R.drawable.ic_view_grid)
                true
            }
            R.id.action_select_all -> {
                // TODO: Implement multi-select functionality
                Toast.makeText(this, "Multi-select coming soon", Toast.LENGTH_SHORT).show()
                true
            }
            R.id.action_settings -> {
                // TODO: Open gallery settings
                Toast.makeText(this, "Settings coming soon", Toast.LENGTH_SHORT).show()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    override fun onResume() {
        super.onResume()
        // Refresh the gallery when returning from other activities
        viewModel.refreshScans()
    }
}

