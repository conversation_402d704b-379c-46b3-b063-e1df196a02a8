package com.scanner3d.app.core

import android.content.Context
import android.graphics.ImageFormat
import android.hardware.camera2.*
import android.media.Image
import android.media.ImageReader
import android.os.Handler
import android.os.HandlerThread
import android.util.Log
import android.util.Size
import android.view.Surface
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.lifecycle.LifecycleOwner
import com.google.ar.core.*
import com.scanner3d.app.data.model.PointCloudData
import com.scanner3d.app.data.model.ScanProgress
import com.scanner3d.app.utils.MeshGenerator
import com.scanner3d.app.utils.TextureMapper
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import org.opencv.android.OpenCVLoaderCallback
import org.opencv.android.BaseLoaderCallback
import org.opencv.core.Mat
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

class ScanningEngine(private val context: Context) {
    
    companion object {
        private const val TAG = "ScanningEngine"
        private const val DEPTH_WIDTH = 640
        private const val DEPTH_HEIGHT = 480
        private const val COLOR_WIDTH = 3840  // 4K width
        private const val COLOR_HEIGHT = 2160 // 4K height
    }
    
    // Camera and AR components
    private var cameraProvider: ProcessCameraProvider? = null
    private var imageCapture: ImageCapture? = null
    private var videoCapture: VideoCapture<Recorder>? = null
    private var imageAnalyzer: ImageAnalysis? = null
    private var preview: Preview? = null
    
    // ARCore session
    private var arSession: Session? = null
    private var arConfig: Config? = null
    
    // OpenCV
    private var isOpenCVLoaded = false
    
    // Processing components
    private val meshGenerator = MeshGenerator()
    private val textureMapper = TextureMapper()
    
    // Threading
    private val cameraExecutor: ExecutorService = Executors.newSingleThreadExecutor()
    private val processingExecutor: ExecutorService = Executors.newFixedThreadPool(2)
    
    // State management
    private val _scanProgress = MutableStateFlow(ScanProgress())
    val scanProgress: StateFlow<ScanProgress> = _scanProgress
    
    private val _pointCloudData = MutableStateFlow<PointCloudData?>(null)
    val pointCloudData: StateFlow<PointCloudData?> = _pointCloudData
    
    private val _isScanning = MutableStateFlow(false)
    val isScanning: StateFlow<Boolean> = _isScanning
    
    // Data storage
    private val capturedFrames = mutableListOf<CapturedFrame>()
    private var currentPointCloud: PointCloud? = null
    
    // OpenCV loader callback
    private val openCVLoaderCallback = object : BaseLoaderCallback(context) {
        override fun onManagerConnected(status: Int) {
            when (status) {
                LoaderCallbackInterface.SUCCESS -> {
                    Log.d(TAG, "OpenCV loaded successfully")
                    isOpenCVLoaded = true
                }
                else -> {
                    super.onManagerConnected(status)
                    Log.e(TAG, "OpenCV initialization failed")
                }
            }
        }
    }
    
    data class CapturedFrame(
        val colorImage: Image,
        val depthImage: Image?,
        val pose: Pose?,
        val timestamp: Long
    )
    
    fun initialize(lifecycleOwner: LifecycleOwner): Boolean {
        return try {
            initializeOpenCV()
            initializeARCore()
            initializeCamera(lifecycleOwner)
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize scanning engine", e)
            false
        }
    }
    
    private fun initializeOpenCV() {
        if (!isOpenCVLoaded) {
            org.opencv.android.OpenCVLoader.initDebug()
            openCVLoaderCallback.onManagerConnected(LoaderCallbackInterface.SUCCESS)
        }
    }
    
    private fun initializeARCore() {
        try {
            arSession = Session(context)
            arConfig = Config(arSession).apply {
                // Enable depth mode for ToF sensor
                depthMode = Config.DepthMode.AUTOMATIC
                // Enable light estimation for better texture mapping
                lightEstimationMode = Config.LightEstimationMode.ENVIRONMENTAL_HDR
                // Enable plane detection for reference
                planeFindingMode = Config.PlaneFindingMode.HORIZONTAL_AND_VERTICAL
                // Enable instant placement for quick scanning
                instantPlacementMode = Config.InstantPlacementMode.LOCAL_Y_UP
            }
            arSession?.configure(arConfig)
            Log.d(TAG, "ARCore initialized successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize ARCore", e)
            throw e
        }
    }
    
    private fun initializeCamera(lifecycleOwner: LifecycleOwner) {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(context)
        cameraProviderFuture.addListener({
            cameraProvider = cameraProviderFuture.get()
            
            // Setup camera use cases
            setupPreview()
            setupImageCapture()
            setupImageAnalysis()
            
            // Bind use cases to camera
            bindCameraUseCases(lifecycleOwner)
            
        }, ContextCompat.getMainExecutor(context))
    }
    
    private fun setupPreview() {
        preview = Preview.Builder()
            .setTargetResolution(Size(COLOR_WIDTH, COLOR_HEIGHT))
            .build()
    }
    
    private fun setupImageCapture() {
        imageCapture = ImageCapture.Builder()
            .setTargetResolution(Size(COLOR_WIDTH, COLOR_HEIGHT))
            .setCaptureMode(ImageCapture.CAPTURE_MODE_MAXIMIZE_QUALITY)
            .build()
    }
    
    private fun setupImageAnalysis() {
        imageAnalyzer = ImageAnalysis.Builder()
            .setTargetResolution(Size(COLOR_WIDTH, COLOR_HEIGHT))
            .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
            .build()
            .also { analysis ->
                analysis.setAnalyzer(cameraExecutor) { imageProxy ->
                    if (_isScanning.value) {
                        processFrame(imageProxy)
                    }
                    imageProxy.close()
                }
            }
    }
    
    private fun bindCameraUseCases(lifecycleOwner: LifecycleOwner) {
        try {
            cameraProvider?.unbindAll()
            
            val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA
            
            cameraProvider?.bindToLifecycle(
                lifecycleOwner,
                cameraSelector,
                preview,
                imageCapture,
                imageAnalyzer
            )
            
            Log.d(TAG, "Camera use cases bound successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to bind camera use cases", e)
        }
    }
    
    fun startScanning() {
        if (_isScanning.value) return
        
        try {
            arSession?.resume()
            _isScanning.value = true
            _scanProgress.value = ScanProgress(isActive = true, progress = 0f)
            capturedFrames.clear()
            
            Log.d(TAG, "Scanning started")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start scanning", e)
            _isScanning.value = false
        }
    }
    
    fun stopScanning() {
        if (!_isScanning.value) return
        
        try {
            _isScanning.value = false
            arSession?.pause()
            
            // Process captured frames into final 3D model
            processingExecutor.execute {
                processCapture()
            }
            
            Log.d(TAG, "Scanning stopped")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to stop scanning", e)
        }
    }
    
    fun pauseScanning() {
        if (!_isScanning.value) return
        
        _scanProgress.value = _scanProgress.value.copy(isPaused = true)
        Log.d(TAG, "Scanning paused")
    }
    
    fun resumeScanning() {
        if (!_isScanning.value) return
        
        _scanProgress.value = _scanProgress.value.copy(isPaused = false)
        Log.d(TAG, "Scanning resumed")
    }
    
    private fun processFrame(imageProxy: ImageProxy) {
        if (_scanProgress.value.isPaused) return
        
        try {
            arSession?.let { session ->
                val frame = session.update()
                val camera = frame.camera
                
                if (camera.trackingState == TrackingState.TRACKING) {
                    // Get depth image if available
                    val depthImage = frame.acquireDepthImage16Bits()
                    
                    // Create captured frame
                    val capturedFrame = CapturedFrame(
                        colorImage = imageProxy.image!!,
                        depthImage = depthImage,
                        pose = camera.pose,
                        timestamp = System.currentTimeMillis()
                    )
                    
                    capturedFrames.add(capturedFrame)
                    
                    // Update progress
                    val progress = (capturedFrames.size / 100f).coerceAtMost(1f) // Assume 100 frames for complete scan
                    _scanProgress.value = _scanProgress.value.copy(progress = progress)
                    
                    // Process point cloud in real-time
                    processPointCloudRealTime(frame)
                    
                    depthImage?.close()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error processing frame", e)
        }
    }
    
    private fun processPointCloudRealTime(frame: Frame) {
        try {
            frame.acquirePointCloud().use { pointCloud ->
                currentPointCloud = pointCloud
                
                val points = FloatArray(pointCloud.points.remaining())
                pointCloud.points.get(points)
                
                val pointCloudData = PointCloudData(
                    points = points,
                    pointCount = points.size / 4, // 4 values per point (x, y, z, confidence)
                    timestamp = System.currentTimeMillis()
                )
                
                _pointCloudData.value = pointCloudData
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error processing point cloud", e)
        }
    }
    
    private fun processCapture() {
        try {
            _scanProgress.value = _scanProgress.value.copy(
                isProcessing = true,
                processingStage = "Generating mesh..."
            )
            
            // Generate mesh from captured frames
            val mesh = meshGenerator.generateMesh(capturedFrames, currentPointCloud)
            
            _scanProgress.value = _scanProgress.value.copy(
                processingStage = "Mapping textures..."
            )
            
            // Apply texture mapping
            val texturedMesh = textureMapper.applyTextures(mesh, capturedFrames)
            
            _scanProgress.value = _scanProgress.value.copy(
                isProcessing = false,
                isComplete = true,
                processingStage = "Complete"
            )
            
            Log.d(TAG, "Capture processing completed")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error processing capture", e)
            _scanProgress.value = _scanProgress.value.copy(
                isProcessing = false,
                hasError = true,
                errorMessage = e.message
            )
        }
    }
    
    fun getPreviewSurface(): Surface? {
        return preview?.surfaceProvider?.let { surfaceProvider ->
            // This would typically be handled by the UI component
            null
        }
    }
    
    fun cleanup() {
        try {
            _isScanning.value = false
            arSession?.close()
            cameraProvider?.unbindAll()
            cameraExecutor.shutdown()
            processingExecutor.shutdown()
            
            Log.d(TAG, "Scanning engine cleaned up")
        } catch (e: Exception) {
            Log.e(TAG, "Error during cleanup", e)
        }
    }
}

