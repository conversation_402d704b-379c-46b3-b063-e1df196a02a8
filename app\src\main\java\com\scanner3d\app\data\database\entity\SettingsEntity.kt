package com.scanner3d.app.data.database.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "settings")
data class SettingsEntity(
    @PrimaryKey
    val key: String,
    val value: String,
    val type: SettingType,
    val category: String,
    val description: String? = null,
    val defaultValue: String? = null,
    val isUserModifiable: Boolean = true,
    val lastModified: Long = System.currentTimeMillis()
)

enum class SettingType {
    STRING,
    INTEGER,
    FLOAT,
    BOOLEAN,
    JSON
}

