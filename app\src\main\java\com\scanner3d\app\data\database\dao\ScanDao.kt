package com.scanner3d.app.data.database.dao

import androidx.room.*
import androidx.lifecycle.LiveData
import com.scanner3d.app.data.database.entity.ScanEntity
import com.scanner3d.app.data.model.Mesh3D
import kotlinx.coroutines.flow.Flow
import java.util.Date

@Dao
interface ScanDao {
    
    @Query("SELECT * FROM scans ORDER BY createdAt DESC")
    fun getAllScans(): Flow<List<ScanEntity>>
    
    @Query("SELECT * FROM scans ORDER BY createdAt DESC")
    fun getAllScansLiveData(): LiveData<List<ScanEntity>>
    
    @Query("SELECT * FROM scans WHERE id = :scanId")
    suspend fun getScanById(scanId: String): ScanEntity?
    
    @Query("SELECT * FROM scans WHERE id = :scanId")
    fun getScanByIdLiveData(scanId: String): LiveData<ScanEntity?>
    
    @Query("SELECT * FROM scans WHERE isFavorite = 1 ORDER BY createdAt DESC")
    fun getFavoriteScans(): Flow<List<ScanEntity>>
    
    @Query("SELECT * FROM scans WHERE createdAt >= :since ORDER BY createdAt DESC")
    fun getRecentScans(since: Date): Flow<List<ScanEntity>>
    
    @Query("SELECT * FROM scans WHERE name LIKE '%' || :query || '%' OR notes LIKE '%' || :query || '%' ORDER BY createdAt DESC")
    fun searchScans(query: String): Flow<List<ScanEntity>>
    
    @Query("SELECT * FROM scans WHERE quality = :quality ORDER BY createdAt DESC")
    fun getScansByQuality(quality: Mesh3D.MeshQuality): Flow<List<ScanEntity>>
    
    @Query("SELECT * FROM scans WHERE hasTexture = :hasTexture ORDER BY createdAt DESC")
    fun getScansByTextureStatus(hasTexture: Boolean): Flow<List<ScanEntity>>
    
    @Query("SELECT * FROM scans WHERE isCloudSynced = :isSynced ORDER BY createdAt DESC")
    fun getScansBySyncStatus(isSynced: Boolean): Flow<List<ScanEntity>>
    
    @Query("SELECT COUNT(*) FROM scans")
    fun getScanCount(): Flow<Int>
    
    @Query("SELECT SUM(fileSize) FROM scans")
    fun getTotalStorageUsed(): Flow<Long?>
    
    @Query("SELECT AVG(scanDuration) FROM scans")
    fun getAverageScanDuration(): Flow<Double?>
    
    @Query("SELECT * FROM scans ORDER BY fileSize DESC LIMIT :limit")
    fun getLargestScans(limit: Int = 10): Flow<List<ScanEntity>>
    
    @Query("SELECT * FROM scans ORDER BY createdAt ASC LIMIT :limit")
    fun getOldestScans(limit: Int = 10): Flow<List<ScanEntity>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertScan(scan: ScanEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertScans(scans: List<ScanEntity>)
    
    @Update
    suspend fun updateScan(scan: ScanEntity)
    
    @Query("UPDATE scans SET isFavorite = :isFavorite WHERE id = :scanId")
    suspend fun updateFavoriteStatus(scanId: String, isFavorite: Boolean)
    
    @Query("UPDATE scans SET name = :newName, modifiedAt = :modifiedAt WHERE id = :scanId")
    suspend fun updateScanName(scanId: String, newName: String, modifiedAt: Date = Date())
    
    @Query("UPDATE scans SET notes = :notes, modifiedAt = :modifiedAt WHERE id = :scanId")
    suspend fun updateScanNotes(scanId: String, notes: String?, modifiedAt: Date = Date())
    
    @Query("UPDATE scans SET isCloudSynced = :isSynced, cloudPath = :cloudPath WHERE id = :scanId")
    suspend fun updateCloudSyncStatus(scanId: String, isSynced: Boolean, cloudPath: String?)
    
    @Query("UPDATE scans SET tags = :tags, modifiedAt = :modifiedAt WHERE id = :scanId")
    suspend fun updateScanTags(scanId: String, tags: List<String>, modifiedAt: Date = Date())
    
    @Delete
    suspend fun deleteScan(scan: ScanEntity)
    
    @Query("DELETE FROM scans WHERE id = :scanId")
    suspend fun deleteScanById(scanId: String)
    
    @Query("DELETE FROM scans WHERE createdAt < :before")
    suspend fun deleteScansOlderThan(before: Date)
    
    @Query("DELETE FROM scans WHERE isFavorite = 0 AND createdAt < :before")
    suspend fun deleteNonFavoriteScansOlderThan(before: Date)
    
    @Query("DELETE FROM scans")
    suspend fun deleteAllScans()
    
    // Batch operations
    @Transaction
    suspend fun insertOrUpdateScan(scan: ScanEntity) {
        val existing = getScanById(scan.id)
        if (existing != null) {
            updateScan(scan.copy(modifiedAt = Date()))
        } else {
            insertScan(scan)
        }
    }
    
    @Transaction
    suspend fun deleteScansWithCleanup(scanIds: List<String>) {
        scanIds.forEach { scanId ->
            deleteScanById(scanId)
        }
    }
}

