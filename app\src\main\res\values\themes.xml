<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.Scanner3D" parent="Theme.Material3.DayNight">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_blue_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/accent_orange</item>
        <item name="colorSecondaryVariant">@color/accent_orange_dark</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
        <item name="android:windowBackground">@color/background_light</item>
        <item name="colorSurface">@color/surface_light</item>
        <item name="colorOnSurface">@color/text_primary_light</item>
    </style>

    <style name="Theme.Scanner3D.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <style name="Theme.Scanner3D.Fullscreen">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowSystemUiVisibility">
            SYSTEM_UI_FLAG_LAYOUT_STABLE |
            SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION |
            SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN |
            SYSTEM_UI_FLAG_HIDE_NAVIGATION |
            SYSTEM_UI_FLAG_FULLSCREEN |
            SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        </item>
    </style>

    <!-- Button Styles -->
    <style name="Scanner3D.Button.Primary" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/button_primary</item>
        <item name="android:textColor">@color/white</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Scanner3D.Button.Secondary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="strokeColor">@color/button_secondary</item>
        <item name="android:textColor">@color/button_secondary</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="Scanner3D.Button.Danger" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/button_danger</item>
        <item name="android:textColor">@color/white</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <!-- Card Styles -->
    <style name="Scanner3D.Card" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="cardBackgroundColor">@color/surface_light</item>
    </style>

    <!-- Text Styles -->
    <style name="Scanner3D.Text.Headline" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:textColor">@color/text_primary_light</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Scanner3D.Text.Title" parent="TextAppearance.Material3.TitleLarge">
        <item name="android:textColor">@color/text_primary_light</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Scanner3D.Text.Body" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textColor">@color/text_primary_light</item>
    </style>

    <style name="Scanner3D.Text.Caption" parent="TextAppearance.Material3.BodySmall">
        <item name="android:textColor">@color/text_secondary_light</item>
    </style>
</resources>

