package com.scanner3d.app.repository

import android.content.Context
import android.content.SharedPreferences
import androidx.biometric.BiometricManager
import androidx.biometric.BiometricPrompt
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume

class AuthRepository(private val context: Context) {
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences("auth_prefs", Context.MODE_PRIVATE)
    
    companion object {
        private const val KEY_IS_AUTHENTICATED = "is_authenticated"
        private const val KEY_AUTH_METHOD = "auth_method"
        private const val KEY_PIN_HASH = "pin_hash"
        private const val AUTH_METHOD_BIOMETRIC = "biometric"
        private const val AUTH_METHOD_PIN = "pin"
    }
    
    fun isUserAuthenticated(): Boolean {
        return sharedPreferences.getBoolean(KEY_IS_AUTHENTICATED, false)
    }
    
    fun setUserAuthenticated(authenticated: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_IS_AUTHENTICATED, authenticated)
            .apply()
    }
    
    fun logout() {
        sharedPreferences.edit()
            .putBoolean(KEY_IS_AUTHENTICATED, false)
            .apply()
    }
    
    fun isBiometricAvailable(): Boolean {
        val biometricManager = BiometricManager.from(context)
        return when (biometricManager.canAuthenticate(BiometricManager.Authenticators.BIOMETRIC_WEAK)) {
            BiometricManager.BIOMETRIC_SUCCESS -> true
            else -> false
        }
    }
    
    suspend fun authenticateWithBiometric(activity: FragmentActivity): Boolean {
        return suspendCancellableCoroutine { continuation ->
            val executor = ContextCompat.getMainExecutor(context)
            val biometricPrompt = BiometricPrompt(activity, executor,
                object : BiometricPrompt.AuthenticationCallback() {
                    override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
                        super.onAuthenticationError(errorCode, errString)
                        if (continuation.isActive) {
                            continuation.resume(false)
                        }
                    }
                    
                    override fun onAuthenticationSucceeded(result: BiometricPrompt.AuthenticationResult) {
                        super.onAuthenticationSucceeded(result)
                        setUserAuthenticated(true)
                        if (continuation.isActive) {
                            continuation.resume(true)
                        }
                    }
                    
                    override fun onAuthenticationFailed() {
                        super.onAuthenticationFailed()
                        if (continuation.isActive) {
                            continuation.resume(false)
                        }
                    }
                })
            
            val promptInfo = BiometricPrompt.PromptInfo.Builder()
                .setTitle("Biometric Authentication")
                .setSubtitle("Use your fingerprint or face to authenticate")
                .setNegativeButtonText("Cancel")
                .build()
            
            biometricPrompt.authenticate(promptInfo)
            
            continuation.invokeOnCancellation {
                // Handle cancellation if needed
            }
        }
    }
    
    fun setPinHash(pinHash: String) {
        sharedPreferences.edit()
            .putString(KEY_PIN_HASH, pinHash)
            .putString(KEY_AUTH_METHOD, AUTH_METHOD_PIN)
            .apply()
    }
    
    fun authenticateWithPin(pin: String): Boolean {
        val storedPinHash = sharedPreferences.getString(KEY_PIN_HASH, null)
        val pinHash = hashPin(pin)
        
        val isValid = storedPinHash == pinHash
        if (isValid) {
            setUserAuthenticated(true)
        }
        return isValid
    }
    
    fun isPinSet(): Boolean {
        return sharedPreferences.getString(KEY_PIN_HASH, null) != null
    }
    
    private fun hashPin(pin: String): String {
        // Simple hash for demonstration - in production, use proper hashing with salt
        return pin.hashCode().toString()
    }
    
    fun getAuthMethod(): String? {
        return sharedPreferences.getString(KEY_AUTH_METHOD, null)
    }
    
    fun setAuthMethod(method: String) {
        sharedPreferences.edit()
            .putString(KEY_AUTH_METHOD, method)
            .apply()
    }
}

