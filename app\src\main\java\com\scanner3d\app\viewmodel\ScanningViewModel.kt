package com.scanner3d.app.viewmodel

import android.app.Application
import androidx.camera.view.PreviewView
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.scanner3d.app.core.ScanningEngine
import com.scanner3d.app.data.model.PointCloudData
import com.scanner3d.app.data.model.ScanProgress
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch

class ScanningViewModel(application: Application) : AndroidViewModel(application) {
    
    private val scanningEngine = ScanningEngine(application)
    
    private val _scanProgress = MutableLiveData<ScanProgress>()
    val scanProgress: LiveData<ScanProgress> = _scanProgress
    
    private val _pointCloudData = MutableLiveData<PointCloudData?>()
    val pointCloudData: LiveData<PointCloudData?> = _pointCloudData
    
    private val _isScanning = MutableLiveData<Boolean>()
    val isScanning: LiveData<Boolean> = _isScanning
    
    private val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage
    
    private val _scanComplete = MutableLiveData<Boolean>()
    val scanComplete: LiveData<Boolean> = _scanComplete
    
    private var currentScanId: String? = null
    
    init {
        // Observe scanning engine state flows
        viewModelScope.launch {
            scanningEngine.scanProgress.collect { progress ->
                _scanProgress.value = progress
                if (progress.isComplete) {
                    _scanComplete.value = true
                    currentScanId = generateScanId()
                }
                if (progress.hasError) {
                    _errorMessage.value = progress.errorMessage
                }
            }
        }
        
        viewModelScope.launch {
            scanningEngine.pointCloudData.collect { pointCloud ->
                _pointCloudData.value = pointCloud
            }
        }
        
        viewModelScope.launch {
            scanningEngine.isScanning.collect { scanning ->
                _isScanning.value = scanning
            }
        }
    }
    
    fun initializeScanning(lifecycleOwner: LifecycleOwner, previewView: PreviewView) {
        viewModelScope.launch {
            try {
                val success = scanningEngine.initialize(lifecycleOwner)
                if (!success) {
                    _errorMessage.value = "Failed to initialize scanning engine"
                } else {
                    // Connect preview view to scanning engine
                    connectPreviewView(previewView)
                }
            } catch (e: Exception) {
                _errorMessage.value = "Initialization error: ${e.message}"
            }
        }
    }
    
    private fun connectPreviewView(previewView: PreviewView) {
        // This would connect the CameraX preview to the PreviewView
        // The actual implementation would depend on how the ScanningEngine
        // exposes its preview surface
        try {
            val surface = scanningEngine.getPreviewSurface()
            // Connect surface to preview view
        } catch (e: Exception) {
            _errorMessage.value = "Failed to connect camera preview: ${e.message}"
        }
    }
    
    fun startScanning() {
        viewModelScope.launch {
            try {
                scanningEngine.startScanning()
                _scanComplete.value = false
                currentScanId = null
            } catch (e: Exception) {
                _errorMessage.value = "Failed to start scanning: ${e.message}"
            }
        }
    }
    
    fun stopScanning() {
        viewModelScope.launch {
            try {
                scanningEngine.stopScanning()
            } catch (e: Exception) {
                _errorMessage.value = "Failed to stop scanning: ${e.message}"
            }
        }
    }
    
    fun pauseScanning() {
        viewModelScope.launch {
            try {
                scanningEngine.pauseScanning()
            } catch (e: Exception) {
                _errorMessage.value = "Failed to pause scanning: ${e.message}"
            }
        }
    }
    
    fun resumeScanning() {
        viewModelScope.launch {
            try {
                scanningEngine.resumeScanning()
            } catch (e: Exception) {
                _errorMessage.value = "Failed to resume scanning: ${e.message}"
            }
        }
    }
    
    fun pauseCamera() {
        // Pause camera when activity is paused
        // Implementation would depend on ScanningEngine API
    }
    
    fun resumeCamera() {
        // Resume camera when activity is resumed
        // Implementation would depend on ScanningEngine API
    }
    
    fun getCurrentScanId(): String? {
        return currentScanId
    }
    
    fun clearError() {
        _errorMessage.value = null
    }
    
    fun cleanup() {
        scanningEngine.cleanup()
    }
    
    private fun generateScanId(): String {
        return "scan_${System.currentTimeMillis()}"
    }
    
    override fun onCleared() {
        super.onCleared()
        cleanup()
    }
}

