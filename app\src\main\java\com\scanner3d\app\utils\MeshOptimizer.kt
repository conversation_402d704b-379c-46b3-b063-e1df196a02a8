package com.scanner3d.app.utils

import android.util.Log
import com.scanner3d.app.data.model.Mesh3D
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.*

class MeshOptimizer {
    
    companion object {
        private const val TAG = "MeshOptimizer"
        private const val DEFAULT_SIMPLIFICATION_RATIO = 0.5f
        private const val MIN_TRIANGLE_AREA = 1e-6f
        private const val VERTEX_MERGE_THRESHOLD = 1e-5f
    }
    
    suspend fun simplifyMesh(
        mesh: Mesh3D,
        targetRatio: Float = DEFAULT_SIMPLIFICATION_RATIO,
        preserveBoundaries: Boolean = true
    ): Mesh3D = withContext(Dispatchers.Default) {
        
        Log.d(TAG, "Starting mesh simplification: ${mesh.triangleCount} triangles -> target: ${(mesh.triangleCount * targetRatio).toInt()}")
        
        try {
            // Step 1: Remove degenerate triangles
            val cleanedMesh = removeDegenerateTriangles(mesh)
            
            // Step 2: Merge duplicate vertices
            val mergedMesh = mergeDuplicateVertices(cleanedMesh)
            
            // Step 3: Edge collapse simplification
            val simplifiedMesh = edgeCollapseSimplification(mergedMesh, targetRatio, preserveBoundaries)
            
            // Step 4: Recalculate normals
            val finalMesh = recalculateNormals(simplifiedMesh)
            
            Log.d(TAG, "Mesh simplification completed: ${mesh.triangleCount} -> ${finalMesh.triangleCount} triangles")
            finalMesh
            
        } catch (e: Exception) {
            Log.e(TAG, "Mesh simplification failed", e)
            throw OptimizationException("Mesh simplification failed: ${e.message}", e)
        }
    }
    
    suspend fun optimizeForRendering(mesh: Mesh3D): Mesh3D = withContext(Dispatchers.Default) {
        try {
            // Optimize vertex order for better cache performance
            val reorderedMesh = optimizeVertexOrder(mesh)
            
            // Generate optimized index buffer
            val optimizedMesh = optimizeIndexBuffer(reorderedMesh)
            
            Log.d(TAG, "Mesh optimized for rendering")
            optimizedMesh
            
        } catch (e: Exception) {
            Log.e(TAG, "Rendering optimization failed", e)
            mesh // Return original mesh if optimization fails
        }
    }
    
    suspend fun generateLODLevels(
        mesh: Mesh3D,
        levels: IntArray = intArrayOf(100, 75, 50, 25, 10) // Percentage of original triangles
    ): List<Mesh3D> = withContext(Dispatchers.Default) {
        
        val lodMeshes = mutableListOf<Mesh3D>()
        
        for (level in levels) {
            val targetRatio = level / 100f
            val lodMesh = simplifyMesh(mesh, targetRatio, preserveBoundaries = true)
            lodMeshes.add(lodMesh)
        }
        
        Log.d(TAG, "Generated ${lodMeshes.size} LOD levels")
        lodMeshes
    }
    
    private fun removeDegenerateTriangles(mesh: Mesh3D): Mesh3D {
        val validTriangles = mutableListOf<Int>()
        val vertices = mesh.vertices
        
        for (i in mesh.indices.indices step 3) {
            val i0 = mesh.indices[i] * 3
            val i1 = mesh.indices[i + 1] * 3
            val i2 = mesh.indices[i + 2] * 3
            
            // Get triangle vertices
            val v0 = floatArrayOf(vertices[i0], vertices[i0 + 1], vertices[i0 + 2])
            val v1 = floatArrayOf(vertices[i1], vertices[i1 + 1], vertices[i1 + 2])
            val v2 = floatArrayOf(vertices[i2], vertices[i2 + 1], vertices[i2 + 2])
            
            // Calculate triangle area
            val area = calculateTriangleArea(v0, v1, v2)
            
            // Keep triangle if area is above threshold
            if (area > MIN_TRIANGLE_AREA) {
                validTriangles.add(mesh.indices[i])
                validTriangles.add(mesh.indices[i + 1])
                validTriangles.add(mesh.indices[i + 2])
            }
        }
        
        return mesh.copy(
            indices = validTriangles.toIntArray(),
            triangleCount = validTriangles.size / 3
        )
    }
    
    private fun mergeDuplicateVertices(mesh: Mesh3D): Mesh3D {
        val vertexMap = mutableMapOf<String, Int>()
        val newVertices = mutableListOf<Float>()
        val newNormals = mutableListOf<Float>()
        val newTexCoords = mutableListOf<Float>()
        val newColors = mutableListOf<Int>()
        val indexMapping = mutableMapOf<Int, Int>()
        
        for (i in 0 until mesh.vertexCount) {
            val vertexIndex = i * 3
            val vertex = floatArrayOf(
                mesh.vertices[vertexIndex],
                mesh.vertices[vertexIndex + 1],
                mesh.vertices[vertexIndex + 2]
            )
            
            val vertexKey = "${vertex[0]}_${vertex[1]}_${vertex[2]}"
            
            val newIndex = vertexMap.getOrPut(vertexKey) {
                // Add new unique vertex
                newVertices.addAll(vertex.toList())
                
                // Add corresponding normal if available
                mesh.normals?.let { normals ->
                    newNormals.add(normals[vertexIndex])
                    newNormals.add(normals[vertexIndex + 1])
                    newNormals.add(normals[vertexIndex + 2])
                }
                
                // Add corresponding texture coordinates if available
                mesh.textureCoordinates?.let { texCoords ->
                    val texIndex = i * 2
                    if (texIndex + 1 < texCoords.size) {
                        newTexCoords.add(texCoords[texIndex])
                        newTexCoords.add(texCoords[texIndex + 1])
                    }
                }
                
                // Add corresponding color if available
                mesh.colors?.let { colors ->
                    if (i < colors.size) {
                        newColors.add(colors[i])
                    }
                }
                
                newVertices.size / 3 - 1
            }
            
            indexMapping[i] = newIndex
        }
        
        // Remap indices
        val newIndices = mesh.indices.map { indexMapping[it] ?: it }.toIntArray()
        
        return mesh.copy(
            vertices = newVertices.toFloatArray(),
            indices = newIndices,
            normals = if (newNormals.isNotEmpty()) newNormals.toFloatArray() else null,
            textureCoordinates = if (newTexCoords.isNotEmpty()) newTexCoords.toFloatArray() else null,
            colors = if (newColors.isNotEmpty()) newColors.toIntArray() else null,
            vertexCount = newVertices.size / 3
        )
    }
    
    private fun edgeCollapseSimplification(
        mesh: Mesh3D,
        targetRatio: Float,
        preserveBoundaries: Boolean
    ): Mesh3D {
        // Simplified edge collapse implementation
        // In a production app, you would use a more sophisticated algorithm like QEM (Quadric Error Metrics)
        
        val targetTriangleCount = (mesh.triangleCount * targetRatio).toInt()
        if (targetTriangleCount >= mesh.triangleCount) {
            return mesh
        }
        
        // Build edge list with collapse costs
        val edges = buildEdgeList(mesh)
        val edgeCosts = calculateEdgeCollapseCosts(mesh, edges)
        
        // Sort edges by collapse cost
        val sortedEdges = edges.zip(edgeCosts).sortedBy { it.second }
        
        var currentMesh = mesh
        var collapsedEdges = 0
        val maxCollapses = mesh.triangleCount - targetTriangleCount
        
        for ((edge, cost) in sortedEdges) {
            if (collapsedEdges >= maxCollapses) break
            
            // Perform edge collapse
            currentMesh = collapseEdge(currentMesh, edge)
            collapsedEdges++
        }
        
        return currentMesh
    }
    
    private fun optimizeVertexOrder(mesh: Mesh3D): Mesh3D {
        // Implement vertex cache optimization (simplified version)
        // This would typically use algorithms like Forsyth or Tipsify
        
        val optimizedIndices = mutableListOf<Int>()
        val processedTriangles = mutableSetOf<Int>()
        val vertexScore = mutableMapOf<Int, Float>()
        
        // Initialize vertex scores
        for (i in 0 until mesh.vertexCount) {
            vertexScore[i] = 0f
        }
        
        // Process triangles in order of vertex cache efficiency
        while (processedTriangles.size < mesh.triangleCount) {
            var bestTriangle = -1
            var bestScore = Float.NEGATIVE_INFINITY
            
            for (t in 0 until mesh.triangleCount) {
                if (t in processedTriangles) continue
                
                val triangleScore = calculateTriangleScore(mesh, t, vertexScore)
                if (triangleScore > bestScore) {
                    bestScore = triangleScore
                    bestTriangle = t
                }
            }
            
            if (bestTriangle >= 0) {
                val baseIndex = bestTriangle * 3
                optimizedIndices.add(mesh.indices[baseIndex])
                optimizedIndices.add(mesh.indices[baseIndex + 1])
                optimizedIndices.add(mesh.indices[baseIndex + 2])
                
                processedTriangles.add(bestTriangle)
                
                // Update vertex scores
                updateVertexScores(mesh.indices, baseIndex, vertexScore)
            }
        }
        
        return mesh.copy(indices = optimizedIndices.toIntArray())
    }
    
    private fun optimizeIndexBuffer(mesh: Mesh3D): Mesh3D {
        // Convert to triangle strips where beneficial
        // This is a simplified implementation
        return mesh // For now, return original mesh
    }
    
    private fun recalculateNormals(mesh: Mesh3D): Mesh3D {
        val normals = FloatArray(mesh.vertices.size)
        val vertexNormalCounts = IntArray(mesh.vertexCount)
        
        // Calculate face normals and accumulate vertex normals
        for (i in mesh.indices.indices step 3) {
            val i0 = mesh.indices[i]
            val i1 = mesh.indices[i + 1]
            val i2 = mesh.indices[i + 2]
            
            val v0Index = i0 * 3
            val v1Index = i1 * 3
            val v2Index = i2 * 3
            
            val v0 = floatArrayOf(mesh.vertices[v0Index], mesh.vertices[v0Index + 1], mesh.vertices[v0Index + 2])
            val v1 = floatArrayOf(mesh.vertices[v1Index], mesh.vertices[v1Index + 1], mesh.vertices[v1Index + 2])
            val v2 = floatArrayOf(mesh.vertices[v2Index], mesh.vertices[v2Index + 1], mesh.vertices[v2Index + 2])
            
            val faceNormal = calculateFaceNormal(v0, v1, v2)
            
            // Accumulate normals for each vertex
            for (j in 0..2) {
                val vertexIndex = when (j) {
                    0 -> i0
                    1 -> i1
                    else -> i2
                }
                val normalIndex = vertexIndex * 3
                
                normals[normalIndex] += faceNormal[0]
                normals[normalIndex + 1] += faceNormal[1]
                normals[normalIndex + 2] += faceNormal[2]
                vertexNormalCounts[vertexIndex]++
            }
        }
        
        // Normalize accumulated normals
        for (i in 0 until mesh.vertexCount) {
            val normalIndex = i * 3
            val count = vertexNormalCounts[i]
            
            if (count > 0) {
                val length = sqrt(
                    normals[normalIndex] * normals[normalIndex] +
                    normals[normalIndex + 1] * normals[normalIndex + 1] +
                    normals[normalIndex + 2] * normals[normalIndex + 2]
                )
                
                if (length > 0) {
                    normals[normalIndex] /= length
                    normals[normalIndex + 1] /= length
                    normals[normalIndex + 2] /= length
                }
            }
        }
        
        return mesh.copy(normals = normals)
    }
    
    private fun calculateTriangleArea(v0: FloatArray, v1: FloatArray, v2: FloatArray): Float {
        val edge1 = floatArrayOf(v1[0] - v0[0], v1[1] - v0[1], v1[2] - v0[2])
        val edge2 = floatArrayOf(v2[0] - v0[0], v2[1] - v0[1], v2[2] - v0[2])
        
        val cross = floatArrayOf(
            edge1[1] * edge2[2] - edge1[2] * edge2[1],
            edge1[2] * edge2[0] - edge1[0] * edge2[2],
            edge1[0] * edge2[1] - edge1[1] * edge2[0]
        )
        
        val length = sqrt(cross[0] * cross[0] + cross[1] * cross[1] + cross[2] * cross[2])
        return length * 0.5f
    }
    
    private fun calculateFaceNormal(v0: FloatArray, v1: FloatArray, v2: FloatArray): FloatArray {
        val edge1 = floatArrayOf(v1[0] - v0[0], v1[1] - v0[1], v1[2] - v0[2])
        val edge2 = floatArrayOf(v2[0] - v0[0], v2[1] - v0[1], v2[2] - v0[2])
        
        val normal = floatArrayOf(
            edge1[1] * edge2[2] - edge1[2] * edge2[1],
            edge1[2] * edge2[0] - edge1[0] * edge2[2],
            edge1[0] * edge2[1] - edge1[1] * edge2[0]
        )
        
        val length = sqrt(normal[0] * normal[0] + normal[1] * normal[1] + normal[2] * normal[2])
        if (length > 0) {
            normal[0] /= length
            normal[1] /= length
            normal[2] /= length
        }
        
        return normal
    }
    
    private fun buildEdgeList(mesh: Mesh3D): List<Pair<Int, Int>> {
        val edges = mutableSetOf<Pair<Int, Int>>()
        
        for (i in mesh.indices.indices step 3) {
            val v0 = mesh.indices[i]
            val v1 = mesh.indices[i + 1]
            val v2 = mesh.indices[i + 2]
            
            // Add edges (ensure consistent ordering)
            edges.add(Pair(minOf(v0, v1), maxOf(v0, v1)))
            edges.add(Pair(minOf(v1, v2), maxOf(v1, v2)))
            edges.add(Pair(minOf(v2, v0), maxOf(v2, v0)))
        }
        
        return edges.toList()
    }
    
    private fun calculateEdgeCollapseCosts(mesh: Mesh3D, edges: List<Pair<Int, Int>>): List<Float> {
        // Simplified cost calculation - in production, use QEM or similar
        return edges.map { edge ->
            val v0Index = edge.first * 3
            val v1Index = edge.second * 3
            
            val v0 = floatArrayOf(mesh.vertices[v0Index], mesh.vertices[v0Index + 1], mesh.vertices[v0Index + 2])
            val v1 = floatArrayOf(mesh.vertices[v1Index], mesh.vertices[v1Index + 1], mesh.vertices[v1Index + 2])
            
            // Edge length as cost (shorter edges have lower cost)
            sqrt(
                (v1[0] - v0[0]) * (v1[0] - v0[0]) +
                (v1[1] - v0[1]) * (v1[1] - v0[1]) +
                (v1[2] - v0[2]) * (v1[2] - v0[2])
            )
        }
    }
    
    private fun collapseEdge(mesh: Mesh3D, edge: Pair<Int, Int>): Mesh3D {
        // Simplified edge collapse - merge second vertex into first
        val vertexToRemove = edge.second
        val vertexToKeep = edge.first
        
        val newIndices = mesh.indices.map { index ->
            if (index == vertexToRemove) vertexToKeep else index
        }.toIntArray()
        
        return mesh.copy(indices = newIndices)
    }
    
    private fun calculateTriangleScore(mesh: Mesh3D, triangleIndex: Int, vertexScore: Map<Int, Float>): Float {
        val baseIndex = triangleIndex * 3
        val v0 = mesh.indices[baseIndex]
        val v1 = mesh.indices[baseIndex + 1]
        val v2 = mesh.indices[baseIndex + 2]
        
        return (vertexScore[v0] ?: 0f) + (vertexScore[v1] ?: 0f) + (vertexScore[v2] ?: 0f)
    }
    
    private fun updateVertexScores(indices: IntArray, triangleBaseIndex: Int, vertexScore: MutableMap<Int, Float>) {
        // Simplified vertex score update for cache optimization
        val cacheDecay = 0.75f
        
        for (i in 0..2) {
            val vertex = indices[triangleBaseIndex + i]
            vertexScore[vertex] = (vertexScore[vertex] ?: 0f) * cacheDecay + 1f
        }
    }
    
    class OptimizationException(message: String, cause: Throwable? = null) : Exception(message, cause)
}

