<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".ui.model.ModelViewerActivity">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary_blue"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <!-- 3D Model Viewer -->
        <com.scanner3d.app.ui.custom.ModelViewer3D
            android:id="@+id/model_viewer"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_margin="8dp"
            android:background="@color/black"
            app:layout_constraintBottom_toTopOf="@+id/ll_viewer_controls"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Loading Progress -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/model_viewer"
            app:layout_constraintEnd_toEndOf="@+id/model_viewer"
            app:layout_constraintStart_toStartOf="@+id/model_viewer"
            app:layout_constraintTop_toTopOf="@+id/model_viewer" />

        <!-- Model Info Panel -->
        <LinearLayout
            android:id="@+id/ll_model_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:background="@drawable/rounded_background"
            android:orientation="vertical"
            android:padding="12dp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_vertex_count"
                style="@style/Scanner3D.Text.Caption"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Vertices: 0" />

            <TextView
                android:id="@+id/tv_triangle_count"
                style="@style/Scanner3D.Text.Caption"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Triangles: 0" />

            <TextView
                android:id="@+id/tv_quality"
                style="@style/Scanner3D.Text.Caption"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Quality: Medium" />

            <TextView
                android:id="@+id/tv_file_size"
                style="@style/Scanner3D.Text.Caption"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Size: 0 MB" />

            <TextView
                android:id="@+id/tv_scan_duration"
                style="@style/Scanner3D.Text.Caption"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Scan Time: 0s" />

            <TextView
                android:id="@+id/tv_has_texture"
                style="@style/Scanner3D.Text.Caption"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Texture: No" />

            <TextView
                android:id="@+id/tv_bounding_box"
                style="@style/Scanner3D.Text.Caption"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Dimensions: 0×0×0 m" />

        </LinearLayout>

        <!-- Export Progress -->
        <LinearLayout
            android:id="@+id/ll_export_progress"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:background="@drawable/rounded_background"
            android:orientation="vertical"
            android:padding="16dp"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/ll_viewer_controls"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <TextView
                android:id="@+id/tv_export_progress"
                style="@style/Scanner3D.Text.Body"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Exporting..."
                android:textColor="@color/white" />

            <ProgressBar
                android:id="@+id/progress_bar_export"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:max="100"
                android:progress="0"
                android:progressTint="@color/primary_blue" />

        </LinearLayout>

        <!-- Viewer Controls -->
        <LinearLayout
            android:id="@+id/ll_viewer_controls"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@color/surface_light"
            android:elevation="4dp"
            android:orientation="vertical"
            android:padding="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <!-- Edit Tools Row -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btn_rotate_model"
                    style="@style/Scanner3D.Button.Secondary"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="4dp"
                    android:layout_weight="1"
                    android:drawableTop="@drawable/ic_rotate"
                    android:drawablePadding="4dp"
                    android:text="Rotate"
                    android:textAllCaps="false"
                    android:textSize="12sp" />

                <Button
                    android:id="@+id/btn_scale_model"
                    style="@style/Scanner3D.Button.Secondary"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="4dp"
                    android:layout_weight="1"
                    android:drawableTop="@drawable/ic_scale"
                    android:drawablePadding="4dp"
                    android:text="Scale"
                    android:textAllCaps="false"
                    android:textSize="12sp" />

                <Button
                    android:id="@+id/btn_translate_model"
                    style="@style/Scanner3D.Button.Secondary"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:drawableTop="@drawable/ic_move"
                    android:drawablePadding="4dp"
                    android:text="Move"
                    android:textAllCaps="false"
                    android:textSize="12sp" />

            </LinearLayout>

            <!-- View Controls Row -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btn_reset_view"
                    style="@style/Scanner3D.Button.Secondary"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="4dp"
                    android:layout_weight="1"
                    android:drawableTop="@drawable/ic_refresh"
                    android:drawablePadding="4dp"
                    android:text="Reset"
                    android:textAllCaps="false"
                    android:textSize="12sp" />

                <Button
                    android:id="@+id/btn_wireframe"
                    style="@style/Scanner3D.Button.Secondary"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="4dp"
                    android:layout_weight="1"
                    android:drawableTop="@drawable/ic_wireframe"
                    android:drawablePadding="4dp"
                    android:text="Wire"
                    android:textAllCaps="false"
                    android:textSize="12sp" />

                <Button
                    android:id="@+id/btn_show_texture"
                    style="@style/Scanner3D.Button.Secondary"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:drawableTop="@drawable/ic_texture"
                    android:drawablePadding="4dp"
                    android:text="Texture"
                    android:textAllCaps="false"
                    android:textSize="12sp" />

            </LinearLayout>

            <!-- Action Buttons Row -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:gravity="center"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btn_toggle_info"
                    style="@style/Scanner3D.Button.Secondary"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    android:drawableStart="@drawable/ic_info"
                    android:drawablePadding="8dp"
                    android:text="Show Info"
                    android:textAllCaps="false" />

                <Button
                    android:id="@+id/btn_export_model"
                    style="@style/Scanner3D.Button.Primary"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    android:drawableStart="@drawable/ic_export"
                    android:drawablePadding="8dp"
                    android:text="@string/export_model"
                    android:textAllCaps="false" />

                <Button
                    android:id="@+id/btn_share_model"
                    style="@style/Scanner3D.Button.Secondary"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    android:drawableStart="@drawable/ic_share"
                    android:drawablePadding="8dp"
                    android:text="@string/share_model"
                    android:textAllCaps="false" />

                <Button
                    android:id="@+id/btn_delete_model"
                    style="@style/Scanner3D.Button.Danger"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:drawableStart="@drawable/ic_delete"
                    android:drawablePadding="8dp"
                    android:text="@string/delete_model"
                    android:textAllCaps="false" />

            </LinearLayout>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>

