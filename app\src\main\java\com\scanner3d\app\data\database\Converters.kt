package com.scanner3d.app.data.database

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.scanner3d.app.data.model.Mesh3D
import java.util.Date

class Converters {
    
    private val gson = Gson()
    
    @TypeConverter
    fun fromTimestamp(value: Long?): Date? {
        return value?.let { Date(it) }
    }
    
    @TypeConverter
    fun dateToTimestamp(date: Date?): Long? {
        return date?.time
    }
    
    @TypeConverter
    fun fromMeshQuality(quality: Mesh3D.MeshQuality): String {
        return quality.name
    }
    
    @TypeConverter
    fun toMeshQuality(quality: String): Mesh3D.MeshQuality {
        return Mesh3D.MeshQuality.valueOf(quality)
    }
    
    @TypeConverter
    fun fromStringList(value: List<String>): String {
        return gson.toJson(value)
    }
    
    @TypeConverter
    fun toStringList(value: String): List<String> {
        val listType = object : TypeToken<List<String>>() {}.type
        return gson.fromJson(value, listType) ?: emptyList()
    }
    
    @TypeConverter
    fun fromSettingType(type: com.scanner3d.app.data.database.entity.SettingType): String {
        return type.name
    }
    
    @TypeConverter
    fun toSettingType(type: String): com.scanner3d.app.data.database.entity.SettingType {
        return com.scanner3d.app.data.database.entity.SettingType.valueOf(type)
    }
}

