package com.scanner3d.app.data.database.dao

import androidx.room.*
import androidx.lifecycle.LiveData
import com.scanner3d.app.data.database.entity.UserPreferencesEntity
import com.scanner3d.app.data.model.Mesh3D
import kotlinx.coroutines.flow.Flow

@Dao
interface UserPreferencesDao {
    
    @Query("SELECT * FROM user_preferences WHERE id = 1")
    suspend fun getUserPreferences(): UserPreferencesEntity?
    
    @Query("SELECT * FROM user_preferences WHERE id = 1")
    fun getUserPreferencesLiveData(): LiveData<UserPreferencesEntity?>
    
    @Query("SELECT * FROM user_preferences WHERE id = 1")
    fun getUserPreferencesFlow(): Flow<UserPreferencesEntity?>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserPreferences(preferences: UserPreferencesEntity)
    
    @Update
    suspend fun updateUserPreferences(preferences: UserPreferencesEntity)
    
    @Query("DELETE FROM user_preferences")
    suspend fun deleteAllUserPreferences()
    
    // Scanning preferences
    @Query("UPDATE user_preferences SET defaultScanQuality = :quality, lastModified = :timestamp WHERE id = 1")
    suspend fun updateDefaultScanQuality(quality: Mesh3D.MeshQuality, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET autoSaveEnabled = :enabled, lastModified = :timestamp WHERE id = 1")
    suspend fun updateAutoSaveEnabled(enabled: Boolean, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET autoSaveInterval = :interval, lastModified = :timestamp WHERE id = 1")
    suspend fun updateAutoSaveInterval(interval: Int, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET maxScanDuration = :duration, lastModified = :timestamp WHERE id = 1")
    suspend fun updateMaxScanDuration(duration: Int, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET enableDepthFiltering = :enabled, lastModified = :timestamp WHERE id = 1")
    suspend fun updateDepthFiltering(enabled: Boolean, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET enableMeshSmoothing = :enabled, lastModified = :timestamp WHERE id = 1")
    suspend fun updateMeshSmoothing(enabled: Boolean, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET textureResolution = :resolution, lastModified = :timestamp WHERE id = 1")
    suspend fun updateTextureResolution(resolution: Int, timestamp: Long = System.currentTimeMillis())
    
    // Storage preferences
    @Query("UPDATE user_preferences SET maxLocalStorageGB = :maxStorage, lastModified = :timestamp WHERE id = 1")
    suspend fun updateMaxLocalStorage(maxStorage: Float, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET compressionEnabled = :enabled, lastModified = :timestamp WHERE id = 1")
    suspend fun updateCompressionEnabled(enabled: Boolean, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET compressionLevel = :level, lastModified = :timestamp WHERE id = 1")
    suspend fun updateCompressionLevel(level: Int, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET autoCleanupEnabled = :enabled, lastModified = :timestamp WHERE id = 1")
    suspend fun updateAutoCleanupEnabled(enabled: Boolean, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET cleanupThresholdDays = :days, lastModified = :timestamp WHERE id = 1")
    suspend fun updateCleanupThreshold(days: Int, timestamp: Long = System.currentTimeMillis())
    
    // Cloud sync preferences
    @Query("UPDATE user_preferences SET cloudSyncEnabled = :enabled, lastModified = :timestamp WHERE id = 1")
    suspend fun updateCloudSyncEnabled(enabled: Boolean, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET autoSyncEnabled = :enabled, lastModified = :timestamp WHERE id = 1")
    suspend fun updateAutoSyncEnabled(enabled: Boolean, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET syncOnWifiOnly = :wifiOnly, lastModified = :timestamp WHERE id = 1")
    suspend fun updateSyncOnWifiOnly(wifiOnly: Boolean, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET syncQuality = :quality, lastModified = :timestamp WHERE id = 1")
    suspend fun updateSyncQuality(quality: Mesh3D.MeshQuality, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET encryptionEnabled = :enabled, lastModified = :timestamp WHERE id = 1")
    suspend fun updateEncryptionEnabled(enabled: Boolean, timestamp: Long = System.currentTimeMillis())
    
    // UI preferences
    @Query("UPDATE user_preferences SET defaultViewMode = :viewMode, lastModified = :timestamp WHERE id = 1")
    suspend fun updateDefaultViewMode(viewMode: String, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET showThumbnails = :show, lastModified = :timestamp WHERE id = 1")
    suspend fun updateShowThumbnails(show: Boolean, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET animationsEnabled = :enabled, lastModified = :timestamp WHERE id = 1")
    suspend fun updateAnimationsEnabled(enabled: Boolean, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET hapticFeedbackEnabled = :enabled, lastModified = :timestamp WHERE id = 1")
    suspend fun updateHapticFeedbackEnabled(enabled: Boolean, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET keepScreenOn = :keepOn, lastModified = :timestamp WHERE id = 1")
    suspend fun updateKeepScreenOn(keepOn: Boolean, timestamp: Long = System.currentTimeMillis())
    
    // Export preferences
    @Query("UPDATE user_preferences SET defaultExportFormat = :format, lastModified = :timestamp WHERE id = 1")
    suspend fun updateDefaultExportFormat(format: String, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET defaultExportQuality = :quality, lastModified = :timestamp WHERE id = 1")
    suspend fun updateDefaultExportQuality(quality: Mesh3D.MeshQuality, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET includeTextures = :include, lastModified = :timestamp WHERE id = 1")
    suspend fun updateIncludeTextures(include: Boolean, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET includeColors = :include, lastModified = :timestamp WHERE id = 1")
    suspend fun updateIncludeColors(include: Boolean, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET exportCompressionEnabled = :enabled, lastModified = :timestamp WHERE id = 1")
    suspend fun updateExportCompressionEnabled(enabled: Boolean, timestamp: Long = System.currentTimeMillis())
    
    // Performance preferences
    @Query("UPDATE user_preferences SET maxRAMUsageGB = :maxRAM, lastModified = :timestamp WHERE id = 1")
    suspend fun updateMaxRAMUsage(maxRAM: Float, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET maxCacheSizeMB = :maxCache, lastModified = :timestamp WHERE id = 1")
    suspend fun updateMaxCacheSize(maxCache: Int, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET gpuAccelerationEnabled = :enabled, lastModified = :timestamp WHERE id = 1")
    suspend fun updateGpuAccelerationEnabled(enabled: Boolean, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET backgroundProcessingEnabled = :enabled, lastModified = :timestamp WHERE id = 1")
    suspend fun updateBackgroundProcessingEnabled(enabled: Boolean, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_preferences SET powerSavingMode = :enabled, lastModified = :timestamp WHERE id = 1")
    suspend fun updatePowerSavingMode(enabled: Boolean, timestamp: Long = System.currentTimeMillis())
    
    @Transaction
    suspend fun getOrCreateUserPreferences(): UserPreferencesEntity {
        return getUserPreferences() ?: run {
            val defaultPreferences = UserPreferencesEntity()
            insertUserPreferences(defaultPreferences)
            defaultPreferences
        }
    }
    
    @Transaction
    suspend fun resetToDefaults() {
        deleteAllUserPreferences()
        insertUserPreferences(UserPreferencesEntity())
    }
}

