package com.scanner3d.app.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.scanner3d.app.data.database.dao.ScanDao
import com.scanner3d.app.data.database.dao.SettingsDao
import com.scanner3d.app.data.database.dao.UserPreferencesDao
import com.scanner3d.app.data.database.entity.ScanEntity
import com.scanner3d.app.data.database.entity.SettingsEntity
import com.scanner3d.app.data.database.entity.UserPreferencesEntity

@Database(
    entities = [
        ScanEntity::class,
        SettingsEntity::class,
        UserPreferencesEntity::class
    ],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class ScanDatabase : RoomDatabase() {
    
    abstract fun scanDao(): ScanDao
    abstract fun settingsDao(): SettingsDao
    abstract fun userPreferencesDao(): UserPreferencesDao
    
    companion object {
        @Volatile
        private var INSTANCE: ScanDatabase? = null
        
        fun getDatabase(context: Context): ScanDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    ScanDatabase::class.java,
                    "scan_database"
                )
                .fallbackToDestructiveMigration()
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
}

