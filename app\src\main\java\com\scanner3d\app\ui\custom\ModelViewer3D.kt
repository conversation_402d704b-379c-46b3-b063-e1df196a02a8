package com.scanner3d.app.ui.custom

import android.content.Context
import android.opengl.GLSurfaceView
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import com.scanner3d.app.data.model.Mesh3D
import javax.microedition.khronos.egl.EGLConfig
import javax.microedition.khronos.opengles.GL10
import kotlin.math.*

class ModelViewer3D @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : GLSurfaceView(context, attrs, defStyleAttr) {
    
    private val renderer = Model3DRenderer()
    private val scaleDetector = ScaleGestureDetector(context, ScaleListener())
    
    // Touch handling
    private var previousX = 0f
    private var previousY = 0f
    private var rotationX = 0f
    private var rotationY = 0f
    private var translationX = 0f
    private var translationY = 0f
    private var scale = 1f
    
    // Interaction modes
    enum class InteractionMode {
        ROTATE, SCALE, TRANSLATE
    }
    
    private var currentMode = InteractionMode.ROTATE
    
    init {
        setEGLContextClientVersion(2)
        setRenderer(renderer)
        renderMode = RENDERMODE_WHEN_DIRTY
    }
    
    fun loadMesh(mesh: Mesh3D) {
        renderer.loadMesh(mesh)
        requestRender()
    }
    
    fun setInteractionMode(mode: InteractionMode) {
        currentMode = mode
    }
    
    fun resetView() {
        rotationX = 0f
        rotationY = 0f
        translationX = 0f
        translationY = 0f
        scale = 1f
        
        renderer.setTransformation(rotationX, rotationY, translationX, translationY, scale)
        requestRender()
    }
    
    fun toggleWireframe() {
        renderer.toggleWireframe()
        requestRender()
    }
    
    fun toggleTexture() {
        renderer.toggleTexture()
        requestRender()
    }
    
    override fun onTouchEvent(event: MotionEvent): Boolean {
        scaleDetector.onTouchEvent(event)
        
        if (!scaleDetector.isInProgress) {
            handleTouchEvent(event)
        }
        
        return true
    }
    
    private fun handleTouchEvent(event: MotionEvent) {
        val x = event.x
        val y = event.y
        
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                previousX = x
                previousY = y
            }
            MotionEvent.ACTION_MOVE -> {
                val deltaX = x - previousX
                val deltaY = y - previousY
                
                when (currentMode) {
                    InteractionMode.ROTATE -> {
                        rotationY += deltaX * 0.5f
                        rotationX += deltaY * 0.5f
                        rotationX = rotationX.coerceIn(-90f, 90f)
                    }
                    InteractionMode.TRANSLATE -> {
                        translationX += deltaX * 0.01f
                        translationY -= deltaY * 0.01f // Invert Y for natural movement
                    }
                    InteractionMode.SCALE -> {
                        val scaleFactor = 1f + (deltaY * 0.01f)
                        scale *= scaleFactor
                        scale = scale.coerceIn(0.1f, 10f)
                    }
                }
                
                renderer.setTransformation(rotationX, rotationY, translationX, translationY, scale)
                requestRender()
                
                previousX = x
                previousY = y
            }
        }
    }
    
    private inner class ScaleListener : ScaleGestureDetector.SimpleOnScaleGestureListener() {
        override fun onScale(detector: ScaleGestureDetector): Boolean {
            scale *= detector.scaleFactor
            scale = scale.coerceIn(0.1f, 10f)
            
            renderer.setTransformation(rotationX, rotationY, translationX, translationY, scale)
            requestRender()
            
            return true
        }
    }
    
    private inner class Model3DRenderer : GLSurfaceView.Renderer {
        
        private var mesh: Mesh3D? = null
        private var rotationX = 0f
        private var rotationY = 0f
        private var translationX = 0f
        private var translationY = 0f
        private var scale = 1f
        private var showWireframe = false
        private var showTexture = true
        
        override fun onSurfaceCreated(gl: GL10, config: EGLConfig) {
            gl.glClearColor(0.2f, 0.2f, 0.2f, 1.0f)
            gl.glEnable(GL10.GL_DEPTH_TEST)
            gl.glEnable(GL10.GL_CULL_FACE)
            gl.glCullFace(GL10.GL_BACK)
            
            // Enable lighting
            gl.glEnable(GL10.GL_LIGHTING)
            gl.glEnable(GL10.GL_LIGHT0)
            
            // Set light properties
            val lightAmbient = floatArrayOf(0.3f, 0.3f, 0.3f, 1.0f)
            val lightDiffuse = floatArrayOf(0.8f, 0.8f, 0.8f, 1.0f)
            val lightPosition = floatArrayOf(1.0f, 1.0f, 1.0f, 0.0f)
            
            gl.glLightfv(GL10.GL_LIGHT0, GL10.GL_AMBIENT, lightAmbient, 0)
            gl.glLightfv(GL10.GL_LIGHT0, GL10.GL_DIFFUSE, lightDiffuse, 0)
            gl.glLightfv(GL10.GL_LIGHT0, GL10.GL_POSITION, lightPosition, 0)
        }
        
        override fun onSurfaceChanged(gl: GL10, width: Int, height: Int) {
            gl.glViewport(0, 0, width, height)
            
            // Set up projection matrix
            gl.glMatrixMode(GL10.GL_PROJECTION)
            gl.glLoadIdentity()
            
            val ratio = width.toFloat() / height.toFloat()
            gl.glFrustumf(-ratio, ratio, -1f, 1f, 1f, 100f)
            
            gl.glMatrixMode(GL10.GL_MODELVIEW)
            gl.glLoadIdentity()
        }
        
        override fun onDrawFrame(gl: GL10) {
            gl.glClear(GL10.GL_COLOR_BUFFER_BIT or GL10.GL_DEPTH_BUFFER_BIT)
            gl.glLoadIdentity()
            
            // Move camera back
            gl.glTranslatef(0f, 0f, -5f)
            
            // Apply transformations
            gl.glTranslatef(translationX, translationY, 0f)
            gl.glScalef(scale, scale, scale)
            gl.glRotatef(rotationX, 1f, 0f, 0f)
            gl.glRotatef(rotationY, 0f, 1f, 0f)
            
            // Draw mesh
            mesh?.let { drawMesh(gl, it) }
            
            // Draw coordinate axes for reference
            drawCoordinateAxes(gl)
        }
        
        private fun drawMesh(gl: GL10, mesh: Mesh3D) {
            gl.glEnableClientState(GL10.GL_VERTEX_ARRAY)
            gl.glEnableClientState(GL10.GL_NORMAL_ARRAY)
            
            // Set vertex array
            val vertexBuffer = java.nio.ByteBuffer.allocateDirect(mesh.vertices.size * 4)
                .order(java.nio.ByteOrder.nativeOrder())
                .asFloatBuffer()
            vertexBuffer.put(mesh.vertices)
            vertexBuffer.position(0)
            
            gl.glVertexPointer(3, GL10.GL_FLOAT, 0, vertexBuffer)
            
            // Set normal array if available
            mesh.normals?.let { normals ->
                val normalBuffer = java.nio.ByteBuffer.allocateDirect(normals.size * 4)
                    .order(java.nio.ByteOrder.nativeOrder())
                    .asFloatBuffer()
                normalBuffer.put(normals)
                normalBuffer.position(0)
                
                gl.glNormalPointer(GL10.GL_FLOAT, 0, normalBuffer)
            }
            
            // Set index array
            val indexBuffer = java.nio.ByteBuffer.allocateDirect(mesh.indices.size * 4)
                .order(java.nio.ByteOrder.nativeOrder())
                .asIntBuffer()
            indexBuffer.put(mesh.indices)
            indexBuffer.position(0)
            
            // Set material properties
            val materialAmbient = floatArrayOf(0.2f, 0.2f, 0.8f, 1.0f)
            val materialDiffuse = floatArrayOf(0.4f, 0.4f, 1.0f, 1.0f)
            val materialSpecular = floatArrayOf(1.0f, 1.0f, 1.0f, 1.0f)
            val materialShininess = floatArrayOf(50.0f)
            
            gl.glMaterialfv(GL10.GL_FRONT_AND_BACK, GL10.GL_AMBIENT, materialAmbient, 0)
            gl.glMaterialfv(GL10.GL_FRONT_AND_BACK, GL10.GL_DIFFUSE, materialDiffuse, 0)
            gl.glMaterialfv(GL10.GL_FRONT_AND_BACK, GL10.GL_SPECULAR, materialSpecular, 0)
            gl.glMaterialfv(GL10.GL_FRONT_AND_BACK, GL10.GL_SHININESS, materialShininess, 0)
            
            // Draw based on mode
            if (showWireframe) {
                gl.glPolygonMode(GL10.GL_FRONT_AND_BACK, GL10.GL_LINE)
                gl.glLineWidth(1f)
                gl.glColor4f(0.8f, 0.8f, 0.8f, 1.0f)
            } else {
                gl.glPolygonMode(GL10.GL_FRONT_AND_BACK, GL10.GL_FILL)
                gl.glEnable(GL10.GL_COLOR_MATERIAL)
            }
            
            // Draw triangles
            gl.glDrawElements(GL10.GL_TRIANGLES, mesh.indices.size, GL10.GL_UNSIGNED_INT, indexBuffer)
            
            gl.glDisableClientState(GL10.GL_VERTEX_ARRAY)
            gl.glDisableClientState(GL10.GL_NORMAL_ARRAY)
        }
        
        private fun drawCoordinateAxes(gl: GL10) {
            gl.glDisable(GL10.GL_LIGHTING)
            gl.glLineWidth(3f)
            gl.glBegin(GL10.GL_LINES)
            
            // X axis - Red
            gl.glColor4f(1f, 0f, 0f, 1f)
            gl.glVertex3f(0f, 0f, 0f)
            gl.glVertex3f(1f, 0f, 0f)
            
            // Y axis - Green
            gl.glColor4f(0f, 1f, 0f, 1f)
            gl.glVertex3f(0f, 0f, 0f)
            gl.glVertex3f(0f, 1f, 0f)
            
            // Z axis - Blue
            gl.glColor4f(0f, 0f, 1f, 1f)
            gl.glVertex3f(0f, 0f, 0f)
            gl.glVertex3f(0f, 0f, 1f)
            
            gl.glEnd()
            gl.glEnable(GL10.GL_LIGHTING)
        }
        
        fun loadMesh(mesh: Mesh3D) {
            this.mesh = mesh
        }
        
        fun setTransformation(rotX: Float, rotY: Float, transX: Float, transY: Float, scaleValue: Float) {
            rotationX = rotX
            rotationY = rotY
            translationX = transX
            translationY = transY
            scale = scaleValue
        }
        
        fun toggleWireframe() {
            showWireframe = !showWireframe
        }
        
        fun toggleTexture() {
            showTexture = !showTexture
        }
    }
}

