package com.scanner3d.app.utils

import android.app.ActivityManager
import android.content.Context
import android.util.Log
import kotlinx.coroutines.*
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.max

class MemoryManager(private val context: Context) {
    
    companion object {
        private const val TAG = "MemoryManager"
        private const val MAX_RAM_USAGE_RATIO = 0.8f // Use max 80% of available RAM
        private const val CACHE_CLEANUP_THRESHOLD = 0.9f // Clean cache when 90% full
        private const val GC_THRESHOLD = 0.85f // Trigger GC when 85% memory used
        private const val MEMORY_CHECK_INTERVAL = 5000L // Check memory every 5 seconds
    }
    
    private val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    private val memoryInfo = ActivityManager.MemoryInfo()
    
    // Memory caches
    private val meshCache = ConcurrentHashMap<String, WeakReference<com.scanner3d.app.data.model.Mesh3D>>()
    private val textureCache = ConcurrentHashMap<String, WeakReference<android.graphics.Bitmap>>()
    private val pointCloudCache = ConcurrentHashMap<String, WeakReference<com.scanner3d.app.data.model.PointCloudData>>()
    
    // Memory monitoring
    private var memoryMonitoringJob: Job? = null
    private val memoryListeners = mutableListOf<MemoryListener>()
    
    // Memory limits
    private var maxRAMUsageBytes: Long = 0
    private var maxCacheSizeBytes: Long = 0
    
    init {
        calculateMemoryLimits()
        startMemoryMonitoring()
    }
    
    interface MemoryListener {
        fun onMemoryWarning(level: MemoryWarningLevel)
        fun onMemoryPressure(availableMemory: Long, totalMemory: Long)
        fun onCacheCleared(cacheType: CacheType, freedBytes: Long)
    }
    
    enum class MemoryWarningLevel {
        LOW,      // 70-80% memory used
        MEDIUM,   // 80-90% memory used
        HIGH,     // 90-95% memory used
        CRITICAL  // >95% memory used
    }
    
    enum class CacheType {
        MESH,
        TEXTURE,
        POINT_CLOUD,
        ALL
    }
    
    data class MemoryStats(
        val totalRAM: Long,
        val availableRAM: Long,
        val usedRAM: Long,
        val appMemoryUsage: Long,
        val maxAppMemory: Long,
        val meshCacheSize: Long,
        val textureCacheSize: Long,
        val pointCloudCacheSize: Long,
        val memoryPressure: Float // 0.0 to 1.0
    )
    
    fun addMemoryListener(listener: MemoryListener) {
        memoryListeners.add(listener)
    }
    
    fun removeMemoryListener(listener: MemoryListener) {
        memoryListeners.remove(listener)
    }
    
    fun getMemoryStats(): MemoryStats {
        activityManager.getMemoryInfo(memoryInfo)
        
        val runtime = Runtime.getRuntime()
        val totalRAM = memoryInfo.totalMem
        val availableRAM = memoryInfo.availMem
        val usedRAM = totalRAM - availableRAM
        
        val appMemoryUsage = runtime.totalMemory() - runtime.freeMemory()
        val maxAppMemory = runtime.maxMemory()
        
        val meshCacheSize = calculateCacheSize(meshCache)
        val textureCacheSize = calculateCacheSize(textureCache)
        val pointCloudCacheSize = calculateCacheSize(pointCloudCache)
        
        val memoryPressure = usedRAM.toFloat() / totalRAM.toFloat()
        
        return MemoryStats(
            totalRAM = totalRAM,
            availableRAM = availableRAM,
            usedRAM = usedRAM,
            appMemoryUsage = appMemoryUsage,
            maxAppMemory = maxAppMemory,
            meshCacheSize = meshCacheSize,
            textureCacheSize = textureCacheSize,
            pointCloudCacheSize = pointCloudCacheSize,
            memoryPressure = memoryPressure
        )
    }
    
    fun cacheMesh(key: String, mesh: com.scanner3d.app.data.model.Mesh3D) {
        if (shouldCacheItem(estimateMeshSize(mesh))) {
            meshCache[key] = WeakReference(mesh)
            Log.d(TAG, "Cached mesh: $key")
        }
    }
    
    fun getCachedMesh(key: String): com.scanner3d.app.data.model.Mesh3D? {
        return meshCache[key]?.get()?.also {
            Log.d(TAG, "Retrieved cached mesh: $key")
        }
    }
    
    fun cacheTexture(key: String, texture: android.graphics.Bitmap) {
        if (shouldCacheItem(texture.byteCount.toLong())) {
            textureCache[key] = WeakReference(texture)
            Log.d(TAG, "Cached texture: $key")
        }
    }
    
    fun getCachedTexture(key: String): android.graphics.Bitmap? {
        return textureCache[key]?.get()?.also {
            Log.d(TAG, "Retrieved cached texture: $key")
        }
    }
    
    fun cachePointCloud(key: String, pointCloud: com.scanner3d.app.data.model.PointCloudData) {
        if (shouldCacheItem(estimatePointCloudSize(pointCloud))) {
            pointCloudCache[key] = WeakReference(pointCloud)
            Log.d(TAG, "Cached point cloud: $key")
        }
    }
    
    fun getCachedPointCloud(key: String): com.scanner3d.app.data.model.PointCloudData? {
        return pointCloudCache[key]?.get()?.also {
            Log.d(TAG, "Retrieved cached point cloud: $key")
        }
    }
    
    fun clearCache(cacheType: CacheType = CacheType.ALL): Long {
        var freedBytes = 0L
        
        when (cacheType) {
            CacheType.MESH -> {
                freedBytes += clearMeshCache()
            }
            CacheType.TEXTURE -> {
                freedBytes += clearTextureCache()
            }
            CacheType.POINT_CLOUD -> {
                freedBytes += clearPointCloudCache()
            }
            CacheType.ALL -> {
                freedBytes += clearMeshCache()
                freedBytes += clearTextureCache()
                freedBytes += clearPointCloudCache()
            }
        }
        
        // Notify listeners
        memoryListeners.forEach { listener ->
            listener.onCacheCleared(cacheType, freedBytes)
        }
        
        Log.d(TAG, "Cleared cache: $cacheType, freed: ${freedBytes / 1024 / 1024} MB")
        return freedBytes
    }
    
    fun forceGarbageCollection() {
        Log.d(TAG, "Forcing garbage collection")
        System.gc()
        System.runFinalization()
        System.gc()
    }
    
    fun optimizeMemoryUsage() {
        val stats = getMemoryStats()
        
        Log.d(TAG, "Optimizing memory usage - pressure: ${String.format("%.1f", stats.memoryPressure * 100)}%")
        
        // Clean up weak references
        cleanupWeakReferences()
        
        // Clear caches if memory pressure is high
        if (stats.memoryPressure > CACHE_CLEANUP_THRESHOLD) {
            clearCache(CacheType.ALL)
        }
        
        // Force GC if memory pressure is very high
        if (stats.memoryPressure > GC_THRESHOLD) {
            forceGarbageCollection()
        }
    }
    
    fun isMemoryAvailable(requiredBytes: Long): Boolean {
        val stats = getMemoryStats()
        return stats.availableRAM >= requiredBytes && 
               (stats.appMemoryUsage + requiredBytes) <= maxRAMUsageBytes
    }
    
    fun getAvailableMemory(): Long {
        val stats = getMemoryStats()
        return minOf(stats.availableRAM, maxRAMUsageBytes - stats.appMemoryUsage)
    }
    
    fun setMemoryLimits(maxRAMUsageGB: Float, maxCacheSizeMB: Int) {
        maxRAMUsageBytes = (maxRAMUsageGB * 1024 * 1024 * 1024).toLong()
        maxCacheSizeBytes = (maxCacheSizeMB * 1024 * 1024).toLong()
        
        Log.d(TAG, "Memory limits updated - RAM: ${maxRAMUsageGB}GB, Cache: ${maxCacheSizeMB}MB")
    }
    
    private fun calculateMemoryLimits() {
        activityManager.getMemoryInfo(memoryInfo)
        
        val totalRAM = memoryInfo.totalMem
        maxRAMUsageBytes = (totalRAM * MAX_RAM_USAGE_RATIO).toLong()
        maxCacheSizeBytes = maxRAMUsageBytes / 4 // Use 25% of max RAM for cache
        
        Log.d(TAG, "Memory limits calculated - Total RAM: ${totalRAM / 1024 / 1024}MB, Max usage: ${maxRAMUsageBytes / 1024 / 1024}MB")
    }
    
    private fun startMemoryMonitoring() {
        memoryMonitoringJob = CoroutineScope(Dispatchers.Default).launch {
            while (isActive) {
                try {
                    val stats = getMemoryStats()
                    checkMemoryPressure(stats)
                    delay(MEMORY_CHECK_INTERVAL)
                } catch (e: Exception) {
                    Log.e(TAG, "Error in memory monitoring", e)
                }
            }
        }
    }
    
    private fun checkMemoryPressure(stats: MemoryStats) {
        val warningLevel = when {
            stats.memoryPressure >= 0.95f -> MemoryWarningLevel.CRITICAL
            stats.memoryPressure >= 0.90f -> MemoryWarningLevel.HIGH
            stats.memoryPressure >= 0.80f -> MemoryWarningLevel.MEDIUM
            stats.memoryPressure >= 0.70f -> MemoryWarningLevel.LOW
            else -> null
        }
        
        warningLevel?.let { level ->
            Log.w(TAG, "Memory warning: $level (${String.format("%.1f", stats.memoryPressure * 100)}%)")
            
            memoryListeners.forEach { listener ->
                listener.onMemoryWarning(level)
                listener.onMemoryPressure(stats.availableRAM, stats.totalRAM)
            }
            
            // Auto-optimize on high memory pressure
            if (level == MemoryWarningLevel.HIGH || level == MemoryWarningLevel.CRITICAL) {
                optimizeMemoryUsage()
            }
        }
    }
    
    private fun shouldCacheItem(itemSize: Long): Boolean {
        val currentCacheSize = calculateTotalCacheSize()
        return (currentCacheSize + itemSize) <= maxCacheSizeBytes
    }
    
    private fun calculateTotalCacheSize(): Long {
        return calculateCacheSize(meshCache) + 
               calculateCacheSize(textureCache) + 
               calculateCacheSize(pointCloudCache)
    }
    
    private fun <T> calculateCacheSize(cache: ConcurrentHashMap<String, WeakReference<T>>): Long {
        var size = 0L
        val iterator = cache.values.iterator()
        
        while (iterator.hasNext()) {
            val ref = iterator.next()
            val item = ref.get()
            
            if (item == null) {
                iterator.remove() // Clean up dead references
            } else {
                size += when (item) {
                    is com.scanner3d.app.data.model.Mesh3D -> estimateMeshSize(item)
                    is android.graphics.Bitmap -> item.byteCount.toLong()
                    is com.scanner3d.app.data.model.PointCloudData -> estimatePointCloudSize(item)
                    else -> 0L
                }
            }
        }
        
        return size
    }
    
    private fun clearMeshCache(): Long {
        val freedBytes = calculateCacheSize(meshCache)
        meshCache.clear()
        return freedBytes
    }
    
    private fun clearTextureCache(): Long {
        val freedBytes = calculateCacheSize(textureCache)
        textureCache.clear()
        return freedBytes
    }
    
    private fun clearPointCloudCache(): Long {
        val freedBytes = calculateCacheSize(pointCloudCache)
        pointCloudCache.clear()
        return freedBytes
    }
    
    private fun cleanupWeakReferences() {
        meshCache.values.removeAll { it.get() == null }
        textureCache.values.removeAll { it.get() == null }
        pointCloudCache.values.removeAll { it.get() == null }
    }
    
    private fun estimateMeshSize(mesh: com.scanner3d.app.data.model.Mesh3D): Long {
        var size = 0L
        
        // Vertices (3 floats per vertex)
        size += mesh.vertices.size * 4L
        
        // Indices (1 int per index)
        size += mesh.indices.size * 4L
        
        // Normals (3 floats per vertex)
        mesh.normals?.let { size += it.size * 4L }
        
        // Texture coordinates (2 floats per vertex)
        mesh.textureCoordinates?.let { size += it.size * 4L }
        
        // Colors (1 int per vertex)
        mesh.colors?.let { size += it.size * 4L }
        
        return size
    }
    
    private fun estimatePointCloudSize(pointCloud: com.scanner3d.app.data.model.PointCloudData): Long {
        var size = 0L
        
        // Points (4 floats per point: x, y, z, w)
        size += pointCloud.points.size * 4L
        
        // Confidence values
        pointCloud.confidence?.let { size += it.size * 4L }
        
        // Colors
        pointCloud.colors?.let { size += it.size * 4L }
        
        return size
    }
    
    fun cleanup() {
        memoryMonitoringJob?.cancel()
        clearCache(CacheType.ALL)
        memoryListeners.clear()
    }
}

