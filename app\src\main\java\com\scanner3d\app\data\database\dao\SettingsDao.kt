package com.scanner3d.app.data.database.dao

import androidx.room.*
import androidx.lifecycle.LiveData
import com.scanner3d.app.data.database.entity.SettingsEntity
import com.scanner3d.app.data.database.entity.SettingType
import kotlinx.coroutines.flow.Flow

@Dao
interface SettingsDao {
    
    @Query("SELECT * FROM settings ORDER BY category, key")
    fun getAllSettings(): Flow<List<SettingsEntity>>
    
    @Query("SELECT * FROM settings WHERE category = :category ORDER BY key")
    fun getSettingsByCategory(category: String): Flow<List<SettingsEntity>>
    
    @Query("SELECT * FROM settings WHERE key = :key")
    suspend fun getSettingByKey(key: String): SettingsEntity?
    
    @Query("SELECT * FROM settings WHERE key = :key")
    fun getSettingByKeyLiveData(key: String): LiveData<SettingsEntity?>
    
    @Query("SELECT value FROM settings WHERE key = :key")
    suspend fun getSettingValue(key: String): String?
    
    @Query("SELECT * FROM settings WHERE type = :type ORDER BY category, key")
    fun getSettingsByType(type: SettingType): Flow<List<SettingsEntity>>
    
    @Query("SELECT * FROM settings WHERE isUserModifiable = 1 ORDER BY category, key")
    fun getUserModifiableSettings(): Flow<List<SettingsEntity>>
    
    @Query("SELECT DISTINCT category FROM settings ORDER BY category")
    fun getSettingCategories(): Flow<List<String>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSetting(setting: SettingsEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSettings(settings: List<SettingsEntity>)
    
    @Update
    suspend fun updateSetting(setting: SettingsEntity)
    
    @Query("UPDATE settings SET value = :value, lastModified = :timestamp WHERE key = :key")
    suspend fun updateSettingValue(key: String, value: String, timestamp: Long = System.currentTimeMillis())
    
    @Delete
    suspend fun deleteSetting(setting: SettingsEntity)
    
    @Query("DELETE FROM settings WHERE key = :key")
    suspend fun deleteSettingByKey(key: String)
    
    @Query("DELETE FROM settings WHERE category = :category")
    suspend fun deleteSettingsByCategory(category: String)
    
    @Query("DELETE FROM settings")
    suspend fun deleteAllSettings()
    
    // Convenience methods for common setting types
    suspend fun getStringSetting(key: String, defaultValue: String = ""): String {
        return getSettingValue(key) ?: defaultValue
    }
    
    suspend fun getIntSetting(key: String, defaultValue: Int = 0): Int {
        return getSettingValue(key)?.toIntOrNull() ?: defaultValue
    }
    
    suspend fun getFloatSetting(key: String, defaultValue: Float = 0f): Float {
        return getSettingValue(key)?.toFloatOrNull() ?: defaultValue
    }
    
    suspend fun getBooleanSetting(key: String, defaultValue: Boolean = false): Boolean {
        return getSettingValue(key)?.toBooleanStrictOrNull() ?: defaultValue
    }
    
    @Transaction
    suspend fun setStringSetting(key: String, value: String, category: String = "general") {
        val setting = getSettingByKey(key)
        if (setting != null) {
            updateSettingValue(key, value)
        } else {
            insertSetting(
                SettingsEntity(
                    key = key,
                    value = value,
                    type = SettingType.STRING,
                    category = category
                )
            )
        }
    }
    
    @Transaction
    suspend fun setIntSetting(key: String, value: Int, category: String = "general") {
        val setting = getSettingByKey(key)
        if (setting != null) {
            updateSettingValue(key, value.toString())
        } else {
            insertSetting(
                SettingsEntity(
                    key = key,
                    value = value.toString(),
                    type = SettingType.INTEGER,
                    category = category
                )
            )
        }
    }
    
    @Transaction
    suspend fun setFloatSetting(key: String, value: Float, category: String = "general") {
        val setting = getSettingByKey(key)
        if (setting != null) {
            updateSettingValue(key, value.toString())
        } else {
            insertSetting(
                SettingsEntity(
                    key = key,
                    value = value.toString(),
                    type = SettingType.FLOAT,
                    category = category
                )
            )
        }
    }
    
    @Transaction
    suspend fun setBooleanSetting(key: String, value: Boolean, category: String = "general") {
        val setting = getSettingByKey(key)
        if (setting != null) {
            updateSettingValue(key, value.toString())
        } else {
            insertSetting(
                SettingsEntity(
                    key = key,
                    value = value.toString(),
                    type = SettingType.BOOLEAN,
                    category = category
                )
            )
        }
    }
}

