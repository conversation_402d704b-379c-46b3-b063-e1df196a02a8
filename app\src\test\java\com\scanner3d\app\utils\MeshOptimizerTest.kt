package com.scanner3d.app.utils

import com.scanner3d.app.data.model.Mesh3D
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

@ExperimentalCoroutinesApi
@RunWith(RobolectricTestRunner::class)
class MeshOptimizerTest {
    
    private lateinit var meshOptimizer: MeshOptimizer
    private lateinit var testMesh: Mesh3D
    
    @Before
    fun setUp() {
        meshOptimizer = MeshOptimizer()
        testMesh = createTestMesh()
    }
    
    @Test
    fun `test mesh simplification reduces triangle count`() = runTest {
        // Given
        val targetRatio = 0.5f
        val originalTriangleCount = testMesh.triangleCount
        
        // When
        val simplifiedMesh = meshOptimizer.simplifyMesh(testMesh, targetRatio)
        
        // Then
        assertNotNull(simplifiedMesh, "Simplified mesh should not be null")
        assertTrue(simplifiedMesh.triangleCount <= originalTriangleCount, 
            "Simplified mesh should have fewer or equal triangles")
        assertTrue(simplifiedMesh.vertexCount > 0, "Simplified mesh should have vertices")
        
        // Check that the simplification ratio is approximately correct (within 20% tolerance)
        val actualRatio = simplifiedMesh.triangleCount.toFloat() / originalTriangleCount.toFloat()
        assertTrue(actualRatio <= targetRatio * 1.2f, 
            "Actual simplification ratio should be close to target")
    }
    
    @Test
    fun `test mesh simplification preserves mesh structure`() = runTest {
        // Given
        val targetRatio = 0.7f
        
        // When
        val simplifiedMesh = meshOptimizer.simplifyMesh(testMesh, targetRatio)
        
        // Then
        assertEquals(simplifiedMesh.vertices.size, simplifiedMesh.vertexCount * 3,
            "Vertex array size should match vertex count")
        assertEquals(simplifiedMesh.indices.size, simplifiedMesh.triangleCount * 3,
            "Index array size should match triangle count")
        
        // Check that all indices are valid
        for (index in simplifiedMesh.indices) {
            assertTrue(index >= 0 && index < simplifiedMesh.vertexCount,
                "All indices should be valid vertex references")
        }
    }
    
    @Test
    fun `test mesh simplification with boundary preservation`() = runTest {
        // Given
        val targetRatio = 0.5f
        val preserveBoundaries = true
        
        // When
        val simplifiedMesh = meshOptimizer.simplifyMesh(testMesh, targetRatio, preserveBoundaries)
        
        // Then
        assertNotNull(simplifiedMesh, "Simplified mesh with boundary preservation should not be null")
        assertTrue(simplifiedMesh.triangleCount > 0, "Simplified mesh should have triangles")
        
        // Boundary preservation should result in better quality (more triangles) than without
        val simplifiedWithoutBoundaries = meshOptimizer.simplifyMesh(testMesh, targetRatio, false)
        assertTrue(simplifiedMesh.triangleCount >= simplifiedWithoutBoundaries.triangleCount,
            "Boundary preservation should maintain more triangles")
    }
    
    @Test
    fun `test rendering optimization maintains mesh data`() = runTest {
        // Given
        val originalVertexCount = testMesh.vertexCount
        val originalTriangleCount = testMesh.triangleCount
        
        // When
        val optimizedMesh = meshOptimizer.optimizeForRendering(testMesh)
        
        // Then
        assertNotNull(optimizedMesh, "Optimized mesh should not be null")
        assertEquals(originalVertexCount, optimizedMesh.vertexCount,
            "Rendering optimization should not change vertex count")
        assertEquals(originalTriangleCount, optimizedMesh.triangleCount,
            "Rendering optimization should not change triangle count")
        
        // Vertex and index data should be preserved
        assertEquals(testMesh.vertices.size, optimizedMesh.vertices.size,
            "Vertex array size should be preserved")
        assertEquals(testMesh.indices.size, optimizedMesh.indices.size,
            "Index array size should be preserved")
    }
    
    @Test
    fun `test LOD generation creates multiple levels`() = runTest {
        // Given
        val lodLevels = intArrayOf(100, 75, 50, 25, 10)
        
        // When
        val lodMeshes = meshOptimizer.generateLODLevels(testMesh, lodLevels)
        
        // Then
        assertEquals(lodLevels.size, lodMeshes.size, "Should generate correct number of LOD levels")
        
        // Check that each LOD level has appropriate triangle count
        for (i in lodMeshes.indices) {
            val expectedRatio = lodLevels[i] / 100f
            val actualRatio = lodMeshes[i].triangleCount.toFloat() / testMesh.triangleCount.toFloat()
            
            assertTrue(actualRatio <= expectedRatio * 1.2f, 
                "LOD level $i should have approximately correct triangle count")
            assertTrue(lodMeshes[i].triangleCount > 0, 
                "LOD level $i should have at least one triangle")
        }
        
        // Check that LOD levels are in descending order of detail
        for (i in 1 until lodMeshes.size) {
            assertTrue(lodMeshes[i].triangleCount <= lodMeshes[i-1].triangleCount,
                "LOD levels should have decreasing triangle counts")
        }
    }
    
    @Test
    fun `test mesh optimization with degenerate triangles`() = runTest {
        // Given
        val meshWithDegenerateTriangles = createMeshWithDegenerateTriangles()
        val originalTriangleCount = meshWithDegenerateTriangles.triangleCount
        
        // When
        val optimizedMesh = meshOptimizer.simplifyMesh(meshWithDegenerateTriangles, 1.0f)
        
        // Then
        assertNotNull(optimizedMesh, "Optimized mesh should not be null")
        assertTrue(optimizedMesh.triangleCount <= originalTriangleCount,
            "Degenerate triangles should be removed")
        
        // All remaining triangles should be valid
        for (i in optimizedMesh.indices.indices step 3) {
            val v0Index = optimizedMesh.indices[i] * 3
            val v1Index = optimizedMesh.indices[i + 1] * 3
            val v2Index = optimizedMesh.indices[i + 2] * 3
            
            val v0 = floatArrayOf(optimizedMesh.vertices[v0Index], optimizedMesh.vertices[v0Index + 1], optimizedMesh.vertices[v0Index + 2])
            val v1 = floatArrayOf(optimizedMesh.vertices[v1Index], optimizedMesh.vertices[v1Index + 1], optimizedMesh.vertices[v1Index + 2])
            val v2 = floatArrayOf(optimizedMesh.vertices[v2Index], optimizedMesh.vertices[v2Index + 1], optimizedMesh.vertices[v2Index + 2])
            
            val area = calculateTriangleArea(v0, v1, v2)
            assertTrue(area > 1e-6f, "All triangles should have non-zero area")
        }
    }
    
    @Test
    fun `test mesh optimization with duplicate vertices`() = runTest {
        // Given
        val meshWithDuplicates = createMeshWithDuplicateVertices()
        val originalVertexCount = meshWithDuplicates.vertexCount
        
        // When
        val optimizedMesh = meshOptimizer.simplifyMesh(meshWithDuplicates, 1.0f)
        
        // Then
        assertNotNull(optimizedMesh, "Optimized mesh should not be null")
        assertTrue(optimizedMesh.vertexCount <= originalVertexCount,
            "Duplicate vertices should be merged")
        
        // All indices should still be valid
        for (index in optimizedMesh.indices) {
            assertTrue(index >= 0 && index < optimizedMesh.vertexCount,
                "All indices should reference valid vertices")
        }
    }
    
    @Test
    fun `test extreme simplification ratios`() = runTest {
        // Test very aggressive simplification
        val aggressiveSimplified = meshOptimizer.simplifyMesh(testMesh, 0.1f)
        assertNotNull(aggressiveSimplified, "Aggressive simplification should not fail")
        assertTrue(aggressiveSimplified.triangleCount > 0, "Should maintain at least some triangles")
        
        // Test no simplification
        val noSimplification = meshOptimizer.simplifyMesh(testMesh, 1.0f)
        assertNotNull(noSimplification, "No simplification should not fail")
        assertTrue(noSimplification.triangleCount <= testMesh.triangleCount,
            "No simplification should not increase triangle count")
        
        // Test over-simplification request
        val overSimplified = meshOptimizer.simplifyMesh(testMesh, 1.5f)
        assertNotNull(overSimplified, "Over-simplification request should not fail")
        assertEquals(testMesh.triangleCount, overSimplified.triangleCount,
            "Over-simplification should return original mesh")
    }
    
    @Test
    fun `test mesh optimization preserves normals when available`() = runTest {
        // Given
        val meshWithNormals = createTestMeshWithNormals()
        
        // When
        val optimizedMesh = meshOptimizer.simplifyMesh(meshWithNormals, 0.8f)
        
        // Then
        assertNotNull(optimizedMesh.normals, "Optimized mesh should preserve normals")
        assertEquals(optimizedMesh.normals!!.size, optimizedMesh.vertexCount * 3,
            "Normal array size should match vertex count")
        
        // Check that normals are unit vectors
        for (i in 0 until optimizedMesh.vertexCount) {
            val normalIndex = i * 3
            val nx = optimizedMesh.normals!![normalIndex]
            val ny = optimizedMesh.normals!![normalIndex + 1]
            val nz = optimizedMesh.normals!![normalIndex + 2]
            
            val length = kotlin.math.sqrt(nx * nx + ny * ny + nz * nz)
            assertTrue(kotlin.math.abs(length - 1.0f) < 0.1f, 
                "Normals should be approximately unit length")
        }
    }
    
    private fun createTestMesh(): Mesh3D {
        // Create a simple cube mesh for testing
        val vertices = floatArrayOf(
            // Front face
            -1f, -1f,  1f,  // 0
             1f, -1f,  1f,  // 1
             1f,  1f,  1f,  // 2
            -1f,  1f,  1f,  // 3
            // Back face
            -1f, -1f, -1f,  // 4
             1f, -1f, -1f,  // 5
             1f,  1f, -1f,  // 6
            -1f,  1f, -1f   // 7
        )
        
        val indices = intArrayOf(
            // Front face
            0, 1, 2,  2, 3, 0,
            // Back face
            4, 6, 5,  6, 4, 7,
            // Left face
            4, 0, 3,  3, 7, 4,
            // Right face
            1, 5, 6,  6, 2, 1,
            // Top face
            3, 2, 6,  6, 7, 3,
            // Bottom face
            4, 5, 1,  1, 0, 4
        )
        
        return Mesh3D(
            vertices = vertices,
            indices = indices,
            normals = null,
            textureCoordinates = null,
            colors = null,
            vertexCount = 8,
            triangleCount = 12,
            boundingBox = Mesh3D.BoundingBox(-1f, -1f, -1f, 1f, 1f, 1f),
            metadata = Mesh3D.MeshMetadata(
                createdAt = System.currentTimeMillis(),
                scanDuration = 1000L,
                quality = Mesh3D.MeshQuality.HIGH,
                hasTexture = false,
                hasColors = false,
                estimatedFileSize = 1024L,
                scannerVersion = "1.0"
            )
        )
    }
    
    private fun createTestMeshWithNormals(): Mesh3D {
        val baseMesh = createTestMesh()
        val normals = FloatArray(baseMesh.vertexCount * 3)
        
        // Generate simple normals (pointing outward from center)
        for (i in 0 until baseMesh.vertexCount) {
            val vertexIndex = i * 3
            val x = baseMesh.vertices[vertexIndex]
            val y = baseMesh.vertices[vertexIndex + 1]
            val z = baseMesh.vertices[vertexIndex + 2]
            
            val length = kotlin.math.sqrt(x * x + y * y + z * z)
            normals[vertexIndex] = x / length
            normals[vertexIndex + 1] = y / length
            normals[vertexIndex + 2] = z / length
        }
        
        return baseMesh.copy(normals = normals)
    }
    
    private fun createMeshWithDegenerateTriangles(): Mesh3D {
        val vertices = floatArrayOf(
            0f, 0f, 0f,  // 0
            1f, 0f, 0f,  // 1
            0f, 1f, 0f,  // 2
            0f, 0f, 0f,  // 3 - duplicate of vertex 0
            1f, 1f, 0f   // 4
        )
        
        val indices = intArrayOf(
            0, 1, 2,  // Valid triangle
            0, 3, 1,  // Degenerate triangle (vertices 0 and 3 are the same)
            1, 4, 2   // Valid triangle
        )
        
        return Mesh3D(
            vertices = vertices,
            indices = indices,
            normals = null,
            textureCoordinates = null,
            colors = null,
            vertexCount = 5,
            triangleCount = 3,
            boundingBox = Mesh3D.BoundingBox(0f, 0f, 0f, 1f, 1f, 0f),
            metadata = Mesh3D.MeshMetadata(
                createdAt = System.currentTimeMillis(),
                scanDuration = 1000L,
                quality = Mesh3D.MeshQuality.MEDIUM,
                hasTexture = false,
                hasColors = false,
                estimatedFileSize = 512L,
                scannerVersion = "1.0"
            )
        )
    }
    
    private fun createMeshWithDuplicateVertices(): Mesh3D {
        val vertices = floatArrayOf(
            0f, 0f, 0f,  // 0
            1f, 0f, 0f,  // 1
            0f, 1f, 0f,  // 2
            0f, 0f, 0f,  // 3 - duplicate of vertex 0
            1f, 0f, 0f,  // 4 - duplicate of vertex 1
            1f, 1f, 0f   // 5
        )
        
        val indices = intArrayOf(
            0, 1, 2,  // Triangle using original vertices
            3, 4, 5   // Triangle using duplicate vertices
        )
        
        return Mesh3D(
            vertices = vertices,
            indices = indices,
            normals = null,
            textureCoordinates = null,
            colors = null,
            vertexCount = 6,
            triangleCount = 2,
            boundingBox = Mesh3D.BoundingBox(0f, 0f, 0f, 1f, 1f, 0f),
            metadata = Mesh3D.MeshMetadata(
                createdAt = System.currentTimeMillis(),
                scanDuration = 1000L,
                quality = Mesh3D.MeshQuality.MEDIUM,
                hasTexture = false,
                hasColors = false,
                estimatedFileSize = 512L,
                scannerVersion = "1.0"
            )
        )
    }
    
    private fun calculateTriangleArea(v0: FloatArray, v1: FloatArray, v2: FloatArray): Float {
        val edge1 = floatArrayOf(v1[0] - v0[0], v1[1] - v0[1], v1[2] - v0[2])
        val edge2 = floatArrayOf(v2[0] - v0[0], v2[1] - v0[1], v2[2] - v0[2])
        
        val cross = floatArrayOf(
            edge1[1] * edge2[2] - edge1[2] * edge2[1],
            edge1[2] * edge2[0] - edge1[0] * edge2[2],
            edge1[0] * edge2[1] - edge1[1] * edge2[0]
        )
        
        val length = kotlin.math.sqrt(cross[0] * cross[0] + cross[1] * cross[1] + cross[2] * cross[2])
        return length * 0.5f
    }
}

