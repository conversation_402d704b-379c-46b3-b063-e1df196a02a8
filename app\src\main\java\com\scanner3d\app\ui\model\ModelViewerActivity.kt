package com.scanner3d.app.ui.model

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Observer
import com.scanner3d.app.R
import com.scanner3d.app.databinding.ActivityModelViewerBinding
import com.scanner3d.app.ui.gallery.GalleryActivity
import com.scanner3d.app.viewmodel.ModelViewerViewModel

class ModelViewerActivity : AppCompatActivity() {
    
    companion object {
        const val EXTRA_SCAN_ID = "scan_id"
        const val EXTRA_MODEL_PATH = "model_path"
    }
    
    private lateinit var binding: ActivityModelViewerBinding
    private val viewModel: ModelViewerViewModel by viewModels()
    
    private var currentScanId: String? = null
    private var currentModelPath: String? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityModelViewerBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // Get scan data from intent
        currentScanId = intent.getStringExtra(EXTRA_SCAN_ID)
        currentModelPath = intent.getStringExtra(EXTRA_MODEL_PATH)
        
        setupUI()
        observeViewModel()
        loadModel()
    }
    
    private fun setupUI() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "3D Model Viewer"
        
        binding.apply {
            // Export button
            btnExportModel.setOnClickListener {
                showExportDialog()
            }
            
            // Edit tools
            btnRotateModel.setOnClickListener {
                modelViewer.setInteractionMode(ModelViewer3D.InteractionMode.ROTATE)
            }
            
            btnScaleModel.setOnClickListener {
                modelViewer.setInteractionMode(ModelViewer3D.InteractionMode.SCALE)
            }
            
            btnTranslateModel.setOnClickListener {
                modelViewer.setInteractionMode(ModelViewer3D.InteractionMode.TRANSLATE)
            }
            
            // View controls
            btnResetView.setOnClickListener {
                modelViewer.resetView()
            }
            
            btnWireframe.setOnClickListener {
                modelViewer.toggleWireframe()
            }
            
            btnShowTexture.setOnClickListener {
                modelViewer.toggleTexture()
            }
            
            // Model info toggle
            btnToggleInfo.setOnClickListener {
                toggleModelInfo()
            }
            
            // Share button
            btnShareModel.setOnClickListener {
                shareModel()
            }
            
            // Delete button
            btnDeleteModel.setOnClickListener {
                showDeleteConfirmation()
            }
        }
    }
    
    private fun observeViewModel() {
        viewModel.mesh3D.observe(this, Observer { mesh ->
            mesh?.let {
                binding.modelViewer.loadMesh(it)
                updateModelInfo(it)
            }
        })
        
        viewModel.isLoading.observe(this, Observer { isLoading ->
            binding.progressBar.visibility = if (isLoading) android.view.View.VISIBLE else android.view.View.GONE
        })
        
        viewModel.errorMessage.observe(this, Observer { error ->
            error?.let {
                Toast.makeText(this, it, Toast.LENGTH_LONG).show()
                viewModel.clearError()
            }
        })
        
        viewModel.exportProgress.observe(this, Observer { progress ->
            updateExportProgress(progress)
        })
        
        viewModel.exportComplete.observe(this, Observer { filePath ->
            filePath?.let {
                Toast.makeText(this, "Model exported to: $it", Toast.LENGTH_LONG).show()
            }
        })
    }
    
    private fun loadModel() {
        currentScanId?.let { scanId ->
            viewModel.loadModelByScanId(scanId)
        } ?: currentModelPath?.let { modelPath ->
            viewModel.loadModelFromPath(modelPath)
        } ?: run {
            Toast.makeText(this, "No model data provided", Toast.LENGTH_LONG).show()
            finish()
        }
    }
    
    private fun updateModelInfo(mesh: com.scanner3d.app.data.model.Mesh3D) {
        binding.apply {
            tvVertexCount.text = "Vertices: ${mesh.vertexCount}"
            tvTriangleCount.text = "Triangles: ${mesh.triangleCount}"
            tvQuality.text = "Quality: ${mesh.metadata.quality}"
            tvFileSize.text = "Size: ${formatFileSize(mesh.metadata.estimatedFileSize)}"
            tvScanDuration.text = "Scan Time: ${formatDuration(mesh.metadata.scanDuration)}"
            tvHasTexture.text = "Texture: ${if (mesh.metadata.hasTexture) "Yes" else "No"}"
            
            // Bounding box info
            val bbox = mesh.boundingBox
            tvBoundingBox.text = "Dimensions: ${String.format("%.2f", bbox.width)} × ${String.format("%.2f", bbox.height)} × ${String.format("%.2f", bbox.depth)} m"
        }
    }
    
    private fun toggleModelInfo() {
        binding.apply {
            val isVisible = llModelInfo.visibility == android.view.View.VISIBLE
            llModelInfo.visibility = if (isVisible) android.view.View.GONE else android.view.View.VISIBLE
            btnToggleInfo.text = if (isVisible) "Show Info" else "Hide Info"
        }
    }
    
    private fun showExportDialog() {
        val exportDialog = ExportOptionsDialog()
        exportDialog.setOnExportListener { format, quality, compression ->
            viewModel.exportModel(format, quality, compression)
        }
        exportDialog.show(supportFragmentManager, "export_dialog")
    }
    
    private fun updateExportProgress(progress: Float) {
        binding.apply {
            if (progress >= 0f && progress < 1f) {
                progressBarExport.visibility = android.view.View.VISIBLE
                progressBarExport.progress = (progress * 100).toInt()
                tvExportProgress.visibility = android.view.View.VISIBLE
                tvExportProgress.text = "Exporting... ${(progress * 100).toInt()}%"
            } else {
                progressBarExport.visibility = android.view.View.GONE
                tvExportProgress.visibility = android.view.View.GONE
            }
        }
    }
    
    private fun shareModel() {
        viewModel.getCurrentModelPath()?.let { modelPath ->
            val shareIntent = Intent().apply {
                action = Intent.ACTION_SEND
                type = "application/octet-stream"
                putExtra(Intent.EXTRA_STREAM, android.net.Uri.fromFile(java.io.File(modelPath)))
                putExtra(Intent.EXTRA_TEXT, "Check out this 3D model I scanned!")
            }
            startActivity(Intent.createChooser(shareIntent, "Share 3D Model"))
        } ?: run {
            Toast.makeText(this, "No model to share", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun showDeleteConfirmation() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Delete Model")
            .setMessage("Are you sure you want to delete this 3D model? This action cannot be undone.")
            .setPositiveButton("Delete") { _, _ ->
                deleteModel()
            }
            .setNegativeButton("Cancel", null)
            .show()
    }
    
    private fun deleteModel() {
        currentScanId?.let { scanId ->
            viewModel.deleteModel(scanId)
            Toast.makeText(this, "Model deleted", Toast.LENGTH_SHORT).show()
            finish()
        }
    }
    
    private fun formatFileSize(bytes: Long): String {
        val kb = bytes / 1024.0
        val mb = kb / 1024.0
        
        return when {
            mb >= 1 -> String.format("%.1f MB", mb)
            kb >= 1 -> String.format("%.1f KB", kb)
            else -> "$bytes B"
        }
    }
    
    private fun formatDuration(millis: Long): String {
        val seconds = millis / 1000
        val minutes = seconds / 60
        val remainingSeconds = seconds % 60
        
        return if (minutes > 0) {
            "${minutes}m ${remainingSeconds}s"
        } else {
            "${remainingSeconds}s"
        }
    }
    
    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_model_viewer, menu)
        return true
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                true
            }
            R.id.action_gallery -> {
                startActivity(Intent(this, GalleryActivity::class.java))
                true
            }
            R.id.action_settings -> {
                // TODO: Open model viewer settings
                Toast.makeText(this, "Settings coming soon", Toast.LENGTH_SHORT).show()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    override fun onResume() {
        super.onResume()
        binding.modelViewer.onResume()
    }
    
    override fun onPause() {
        super.onPause()
        binding.modelViewer.onPause()
    }
}

