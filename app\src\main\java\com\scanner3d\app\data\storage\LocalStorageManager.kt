package com.scanner3d.app.data.storage

import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import com.scanner3d.app.data.model.Mesh3D
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream
import org.apache.commons.compress.compressors.gzip.GzipCompressorOutputStream
import java.io.*
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.security.MessageDigest
import kotlin.math.min

class LocalStorageManager(private val context: Context) {
    
    companion object {
        private const val TAG = "LocalStorageManager"
        private const val SCANS_DIRECTORY = "scans"
        private const val THUMBNAILS_DIRECTORY = "thumbnails"
        private const val CACHE_DIRECTORY = "cache"
        private const val TEMP_DIRECTORY = "temp"
        private const val MAX_STORAGE_GB = 1.0f
        private const val COMPRESSION_BUFFER_SIZE = 8192
        
        // File extensions
        private const val MESH_EXTENSION = ".s3d" // Scanner 3D format
        private const val THUMBNAIL_EXTENSION = ".jpg"
        private const val COMPRESSED_EXTENSION = ".gz"
    }
    
    private val scansDir: File by lazy {
        File(context.filesDir, SCANS_DIRECTORY).apply { mkdirs() }
    }
    
    private val thumbnailsDir: File by lazy {
        File(context.filesDir, THUMBNAILS_DIRECTORY).apply { mkdirs() }
    }
    
    private val cacheDir: File by lazy {
        File(context.cacheDir, CACHE_DIRECTORY).apply { mkdirs() }
    }
    
    private val tempDir: File by lazy {
        File(context.cacheDir, TEMP_DIRECTORY).apply { mkdirs() }
    }
    
    suspend fun saveMesh(scanId: String, mesh: Mesh3D, compress: Boolean = true): String = withContext(Dispatchers.IO) {
        try {
            val fileName = "$scanId$MESH_EXTENSION${if (compress) COMPRESSED_EXTENSION else ""}"
            val file = File(scansDir, fileName)
            
            val meshData = serializeMesh(mesh)
            
            if (compress) {
                compressAndSaveData(meshData, file)
            } else {
                file.writeBytes(meshData)
            }
            
            Log.d(TAG, "Mesh saved: ${file.absolutePath} (${file.length()} bytes)")
            file.absolutePath
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save mesh", e)
            throw StorageException("Failed to save mesh: ${e.message}", e)
        }
    }
    
    suspend fun loadMesh(filePath: String): Mesh3D = withContext(Dispatchers.IO) {
        try {
            val file = File(filePath)
            if (!file.exists()) {
                throw StorageException("Mesh file not found: $filePath")
            }
            
            val meshData = if (filePath.endsWith(COMPRESSED_EXTENSION)) {
                decompressData(file)
            } else {
                file.readBytes()
            }
            
            deserializeMesh(meshData)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to load mesh", e)
            throw StorageException("Failed to load mesh: ${e.message}", e)
        }
    }
    
    suspend fun saveThumbnail(scanId: String, thumbnail: Bitmap): String = withContext(Dispatchers.IO) {
        try {
            val fileName = "$scanId$THUMBNAIL_EXTENSION"
            val file = File(thumbnailsDir, fileName)
            
            FileOutputStream(file).use { fos ->
                thumbnail.compress(Bitmap.CompressFormat.JPEG, 85, fos)
            }
            
            Log.d(TAG, "Thumbnail saved: ${file.absolutePath}")
            file.absolutePath
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save thumbnail", e)
            throw StorageException("Failed to save thumbnail: ${e.message}", e)
        }
    }
    
    suspend fun loadThumbnail(filePath: String): Bitmap? = withContext(Dispatchers.IO) {
        try {
            val file = File(filePath)
            if (!file.exists()) return@withContext null
            
            android.graphics.BitmapFactory.decodeFile(file.absolutePath)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to load thumbnail", e)
            null
        }
    }
    
    suspend fun deleteMesh(filePath: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val file = File(filePath)
            val deleted = file.delete()
            
            if (deleted) {
                Log.d(TAG, "Mesh deleted: $filePath")
            } else {
                Log.w(TAG, "Failed to delete mesh: $filePath")
            }
            
            deleted
            
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting mesh", e)
            false
        }
    }
    
    suspend fun deleteThumbnail(filePath: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val file = File(filePath)
            val deleted = file.delete()
            
            if (deleted) {
                Log.d(TAG, "Thumbnail deleted: $filePath")
            }
            
            deleted
            
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting thumbnail", e)
            false
        }
    }
    
    suspend fun getStorageInfo(): StorageInfo = withContext(Dispatchers.IO) {
        val totalSpace = context.filesDir.totalSpace
        val freeSpace = context.filesDir.freeSpace
        val usedSpace = totalSpace - freeSpace
        
        val scansSize = calculateDirectorySize(scansDir)
        val thumbnailsSize = calculateDirectorySize(thumbnailsDir)
        val cacheSize = calculateDirectorySize(cacheDir)
        
        val maxStorageBytes = (MAX_STORAGE_GB * 1024 * 1024 * 1024).toLong()
        val availableForApp = min(freeSpace, maxStorageBytes - scansSize - thumbnailsSize)
        
        StorageInfo(
            totalSpace = totalSpace,
            freeSpace = freeSpace,
            usedSpace = usedSpace,
            scansSize = scansSize,
            thumbnailsSize = thumbnailsSize,
            cacheSize = cacheSize,
            maxAppStorage = maxStorageBytes,
            availableForApp = availableForApp
        )
    }
    
    suspend fun cleanupOldFiles(maxAgeMillis: Long): Int = withContext(Dispatchers.IO) {
        var deletedCount = 0
        val currentTime = System.currentTimeMillis()
        
        // Clean up temp files
        tempDir.listFiles()?.forEach { file ->
            if (currentTime - file.lastModified() > maxAgeMillis) {
                if (file.delete()) {
                    deletedCount++
                }
            }
        }
        
        // Clean up cache files
        cacheDir.listFiles()?.forEach { file ->
            if (currentTime - file.lastModified() > maxAgeMillis) {
                if (file.delete()) {
                    deletedCount++
                }
            }
        }
        
        Log.d(TAG, "Cleaned up $deletedCount old files")
        deletedCount
    }
    
    suspend fun compressMesh(filePath: String): String = withContext(Dispatchers.IO) {
        try {
            val originalFile = File(filePath)
            if (!originalFile.exists()) {
                throw StorageException("Original file not found: $filePath")
            }
            
            val compressedPath = "$filePath$COMPRESSED_EXTENSION"
            val compressedFile = File(compressedPath)
            
            val originalData = originalFile.readBytes()
            compressAndSaveData(originalData, compressedFile)
            
            // Delete original file after successful compression
            originalFile.delete()
            
            Log.d(TAG, "Mesh compressed: ${originalFile.length()} -> ${compressedFile.length()} bytes")
            compressedPath
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to compress mesh", e)
            throw StorageException("Failed to compress mesh: ${e.message}", e)
        }
    }
    
    suspend fun decompressMesh(filePath: String): String = withContext(Dispatchers.IO) {
        try {
            val compressedFile = File(filePath)
            if (!compressedFile.exists()) {
                throw StorageException("Compressed file not found: $filePath")
            }
            
            val decompressedPath = filePath.removeSuffix(COMPRESSED_EXTENSION)
            val decompressedFile = File(decompressedPath)
            
            val decompressedData = decompressData(compressedFile)
            decompressedFile.writeBytes(decompressedData)
            
            Log.d(TAG, "Mesh decompressed: ${compressedFile.length()} -> ${decompressedFile.length()} bytes")
            decompressedPath
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to decompress mesh", e)
            throw StorageException("Failed to decompress mesh: ${e.message}", e)
        }
    }
    
    private fun serializeMesh(mesh: Mesh3D): ByteArray {
        val buffer = ByteArrayOutputStream()
        val dataOutput = DataOutputStream(buffer)
        
        try {
            // Write header
            dataOutput.writeUTF("S3D_MESH_V1") // Format version
            dataOutput.writeLong(System.currentTimeMillis()) // Timestamp
            
            // Write mesh data
            dataOutput.writeInt(mesh.vertexCount)
            dataOutput.writeInt(mesh.triangleCount)
            
            // Write vertices
            dataOutput.writeInt(mesh.vertices.size)
            mesh.vertices.forEach { dataOutput.writeFloat(it) }
            
            // Write indices
            dataOutput.writeInt(mesh.indices.size)
            mesh.indices.forEach { dataOutput.writeInt(it) }
            
            // Write normals (optional)
            val hasNormals = mesh.normals != null
            dataOutput.writeBoolean(hasNormals)
            if (hasNormals) {
                mesh.normals!!.forEach { dataOutput.writeFloat(it) }
            }
            
            // Write texture coordinates (optional)
            val hasTexCoords = mesh.textureCoordinates != null
            dataOutput.writeBoolean(hasTexCoords)
            if (hasTexCoords) {
                dataOutput.writeInt(mesh.textureCoordinates!!.size)
                mesh.textureCoordinates!!.forEach { dataOutput.writeFloat(it) }
            }
            
            // Write colors (optional)
            val hasColors = mesh.colors != null
            dataOutput.writeBoolean(hasColors)
            if (hasColors) {
                dataOutput.writeInt(mesh.colors!!.size)
                mesh.colors!!.forEach { dataOutput.writeInt(it) }
            }
            
            // Write bounding box
            with(mesh.boundingBox) {
                dataOutput.writeFloat(minX)
                dataOutput.writeFloat(minY)
                dataOutput.writeFloat(minZ)
                dataOutput.writeFloat(maxX)
                dataOutput.writeFloat(maxY)
                dataOutput.writeFloat(maxZ)
            }
            
            // Write metadata
            with(mesh.metadata) {
                dataOutput.writeLong(createdAt)
                dataOutput.writeLong(scanDuration)
                dataOutput.writeUTF(quality.name)
                dataOutput.writeBoolean(hasTexture)
                dataOutput.writeBoolean(hasColors)
                dataOutput.writeLong(estimatedFileSize)
                dataOutput.writeUTF(scannerVersion)
            }
            
            dataOutput.flush()
            return buffer.toByteArray()
            
        } finally {
            dataOutput.close()
        }
    }
    
    private fun deserializeMesh(data: ByteArray): Mesh3D {
        val buffer = ByteArrayInputStream(data)
        val dataInput = DataInputStream(buffer)
        
        try {
            // Read header
            val formatVersion = dataInput.readUTF()
            if (formatVersion != "S3D_MESH_V1") {
                throw StorageException("Unsupported mesh format: $formatVersion")
            }
            val timestamp = dataInput.readLong()
            
            // Read mesh data
            val vertexCount = dataInput.readInt()
            val triangleCount = dataInput.readInt()
            
            // Read vertices
            val verticesSize = dataInput.readInt()
            val vertices = FloatArray(verticesSize) { dataInput.readFloat() }
            
            // Read indices
            val indicesSize = dataInput.readInt()
            val indices = IntArray(indicesSize) { dataInput.readInt() }
            
            // Read normals (optional)
            val normals = if (dataInput.readBoolean()) {
                FloatArray(vertices.size) { dataInput.readFloat() }
            } else null
            
            // Read texture coordinates (optional)
            val textureCoordinates = if (dataInput.readBoolean()) {
                val texCoordsSize = dataInput.readInt()
                FloatArray(texCoordsSize) { dataInput.readFloat() }
            } else null
            
            // Read colors (optional)
            val colors = if (dataInput.readBoolean()) {
                val colorsSize = dataInput.readInt()
                IntArray(colorsSize) { dataInput.readInt() }
            } else null
            
            // Read bounding box
            val boundingBox = Mesh3D.BoundingBox(
                minX = dataInput.readFloat(),
                minY = dataInput.readFloat(),
                minZ = dataInput.readFloat(),
                maxX = dataInput.readFloat(),
                maxY = dataInput.readFloat(),
                maxZ = dataInput.readFloat()
            )
            
            // Read metadata
            val metadata = Mesh3D.MeshMetadata(
                createdAt = dataInput.readLong(),
                scanDuration = dataInput.readLong(),
                quality = Mesh3D.MeshQuality.valueOf(dataInput.readUTF()),
                hasTexture = dataInput.readBoolean(),
                hasColors = dataInput.readBoolean(),
                estimatedFileSize = dataInput.readLong(),
                scannerVersion = dataInput.readUTF()
            )
            
            return Mesh3D(
                vertices = vertices,
                indices = indices,
                normals = normals,
                textureCoordinates = textureCoordinates,
                colors = colors,
                vertexCount = vertexCount,
                triangleCount = triangleCount,
                boundingBox = boundingBox,
                metadata = metadata
            )
            
        } finally {
            dataInput.close()
        }
    }
    
    private fun compressAndSaveData(data: ByteArray, outputFile: File) {
        FileOutputStream(outputFile).use { fos ->
            GzipCompressorOutputStream(fos).use { gzos ->
                gzos.write(data)
            }
        }
    }
    
    private fun decompressData(inputFile: File): ByteArray {
        FileInputStream(inputFile).use { fis ->
            GzipCompressorInputStream(fis).use { gzis ->
                return gzis.readBytes()
            }
        }
    }
    
    private fun calculateDirectorySize(directory: File): Long {
        var size = 0L
        directory.listFiles()?.forEach { file ->
            size += if (file.isDirectory) {
                calculateDirectorySize(file)
            } else {
                file.length()
            }
        }
        return size
    }
    
    fun generateFileHash(filePath: String): String {
        val file = File(filePath)
        val digest = MessageDigest.getInstance("SHA-256")
        
        FileInputStream(file).use { fis ->
            val buffer = ByteArray(COMPRESSION_BUFFER_SIZE)
            var bytesRead: Int
            while (fis.read(buffer).also { bytesRead = it } != -1) {
                digest.update(buffer, 0, bytesRead)
            }
        }
        
        return digest.digest().joinToString("") { "%02x".format(it) }
    }
    
    data class StorageInfo(
        val totalSpace: Long,
        val freeSpace: Long,
        val usedSpace: Long,
        val scansSize: Long,
        val thumbnailsSize: Long,
        val cacheSize: Long,
        val maxAppStorage: Long,
        val availableForApp: Long
    ) {
        val isStorageFull: Boolean
            get() = availableForApp <= 0
        
        val storageUsagePercentage: Float
            get() = ((scansSize + thumbnailsSize).toFloat() / maxAppStorage.toFloat() * 100f).coerceAtMost(100f)
    }
    
    class StorageException(message: String, cause: Throwable? = null) : Exception(message, cause)
}

