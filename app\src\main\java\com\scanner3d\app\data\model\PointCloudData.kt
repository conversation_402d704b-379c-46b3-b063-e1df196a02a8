package com.scanner3d.app.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class PointCloudData(
    val points: FloatArray,
    val pointCount: Int,
    val timestamp: Long,
    val confidence: FloatArray? = null,
    val colors: IntArray? = null
) : Parcelable {
    
    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as PointCloudData

        if (!points.contentEquals(other.points)) return false
        if (pointCount != other.pointCount) return false
        if (timestamp != other.timestamp) return false
        if (confidence != null) {
            if (other.confidence == null) return false
            if (!confidence.contentEquals(other.confidence)) return false
        } else if (other.confidence != null) return false
        if (colors != null) {
            if (other.colors == null) return false
            if (!colors.contentEquals(other.colors)) return false
        } else if (other.colors != null) return false

        return true
    }

    override fun hashCode(): Int {
        var result = points.contentHashCode()
        result = 31 * result + pointCount
        result = 31 * result + timestamp.hashCode()
        result = 31 * result + (confidence?.contentHashCode() ?: 0)
        result = 31 * result + (colors?.contentHashCode() ?: 0)
        return result
    }
}

