package com.scanner3d.app.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class Mesh3D(
    val vertices: FloatArray,
    val indices: IntArray,
    val normals: FloatArray? = null,
    val textureCoordinates: FloatArray? = null,
    val colors: IntArray? = null,
    val vertexCount: Int,
    val triangleCount: Int,
    val boundingBox: BoundingBox,
    val metadata: MeshMetadata
) : Parcelable {
    
    @Parcelize
    data class BoundingBox(
        val minX: Float,
        val minY: Float,
        val minZ: Float,
        val maxX: Float,
        val maxY: Float,
        val maxZ: Float
    ) : Parcelable {
        
        val width: Float get() = maxX - minX
        val height: Float get() = maxY - minY
        val depth: Float get() = maxZ - minZ
        val volume: Float get() = width * height * depth
        
        val center: Triple<Float, Float, Float>
            get() = Triple(
                (minX + maxX) / 2f,
                (minY + maxY) / 2f,
                (minZ + maxZ) / 2f
            )
    }
    
    @Parcelize
    data class MeshMetadata(
        val createdAt: Long,
        val scanDuration: Long,
        val quality: MeshQuality,
        val hasTexture: Boolean,
        val hasColors: Boolean,
        val estimatedFileSize: Long,
        val scannerVersion: String
    ) : Parcelable
    
    enum class MeshQuality {
        LOW, MEDIUM, HIGH, ULTRA
    }
    
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as Mesh3D

        if (!vertices.contentEquals(other.vertices)) return false
        if (!indices.contentEquals(other.indices)) return false
        if (normals != null) {
            if (other.normals == null) return false
            if (!normals.contentEquals(other.normals)) return false
        } else if (other.normals != null) return false
        if (textureCoordinates != null) {
            if (other.textureCoordinates == null) return false
            if (!textureCoordinates.contentEquals(other.textureCoordinates)) return false
        } else if (other.textureCoordinates != null) return false
        if (colors != null) {
            if (other.colors == null) return false
            if (!colors.contentEquals(other.colors)) return false
        } else if (other.colors != null) return false
        if (vertexCount != other.vertexCount) return false
        if (triangleCount != other.triangleCount) return false
        if (boundingBox != other.boundingBox) return false
        if (metadata != other.metadata) return false

        return true
    }

    override fun hashCode(): Int {
        var result = vertices.contentHashCode()
        result = 31 * result + indices.contentHashCode()
        result = 31 * result + (normals?.contentHashCode() ?: 0)
        result = 31 * result + (textureCoordinates?.contentHashCode() ?: 0)
        result = 31 * result + (colors?.contentHashCode() ?: 0)
        result = 31 * result + vertexCount
        result = 31 * result + triangleCount
        result = 31 * result + boundingBox.hashCode()
        result = 31 * result + metadata.hashCode()
        return result
    }
}

