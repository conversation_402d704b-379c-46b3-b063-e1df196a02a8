package com.scanner3d.app

import android.content.Context
import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.filters.LargeTest
import com.scanner3d.app.data.database.ScanDatabase
import com.scanner3d.app.data.database.entity.ScanEntity
import com.scanner3d.app.data.model.Mesh3D
import com.scanner3d.app.data.storage.LocalStorageManager
import com.scanner3d.app.utils.MemoryManager
import com.scanner3d.app.utils.MeshOptimizer
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import java.util.Date
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

@ExperimentalCoroutinesApi
@LargeTest
@RunWith(AndroidJUnit4::class)
class ScanningIntegrationTest {
    
    private lateinit var context: Context
    private lateinit var database: ScanDatabase
    private lateinit var storageManager: LocalStorageManager
    private lateinit var memoryManager: MemoryManager
    private lateinit var meshOptimizer: MeshOptimizer
    
    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        
        // Create in-memory database for testing
        database = Room.inMemoryDatabaseBuilder(context, ScanDatabase::class.java)
            .allowMainThreadQueries()
            .build()
        
        storageManager = LocalStorageManager(context)
        memoryManager = MemoryManager(context)
        meshOptimizer = MeshOptimizer()
    }
    
    @After
    fun tearDown() {
        database.close()
        memoryManager.cleanup()
    }
    
    @Test
    fun testCompleteScanningSaveAndLoadWorkflow() = runTest {
        // Given - Create a test mesh
        val testMesh = createTestMesh()
        val scanId = "test_scan_${System.currentTimeMillis()}"
        
        // When - Save mesh to storage
        val savedPath = storageManager.saveMesh(scanId, testMesh, compress = true)
        
        // Then - Verify file was saved
        assertNotNull(savedPath, "Mesh should be saved successfully")
        assertTrue(savedPath.isNotEmpty(), "Saved path should not be empty")
        
        // When - Load mesh from storage
        val loadedMesh = storageManager.loadMesh(savedPath)
        
        // Then - Verify loaded mesh matches original
        assertEquals(testMesh.vertexCount, loadedMesh.vertexCount, "Vertex count should match")
        assertEquals(testMesh.triangleCount, loadedMesh.triangleCount, "Triangle count should match")
        assertEquals(testMesh.vertices.size, loadedMesh.vertices.size, "Vertex array size should match")
        assertEquals(testMesh.indices.size, loadedMesh.indices.size, "Index array size should match")
    }
    
    @Test
    fun testDatabaseScanEntityOperations() = runTest {
        // Given - Create a scan entity
        val scanEntity = ScanEntity(
            id = "test_scan_db_${System.currentTimeMillis()}",
            name = "Test Scan",
            filePath = "/test/path/scan.s3d",
            thumbnailPath = "/test/path/thumbnail.jpg",
            createdAt = Date(),
            modifiedAt = Date(),
            fileSize = 1024L,
            vertexCount = 100,
            triangleCount = 50,
            quality = Mesh3D.MeshQuality.HIGH,
            hasTexture = true,
            hasColors = false,
            scanDuration = 5000L,
            boundingBoxMinX = -1f,
            boundingBoxMinY = -1f,
            boundingBoxMinZ = -1f,
            boundingBoxMaxX = 1f,
            boundingBoxMaxY = 1f,
            boundingBoxMaxZ = 1f,
            isFavorite = false,
            isCloudSynced = false
        )
        
        // When - Insert scan into database
        database.scanDao().insertScan(scanEntity)
        
        // Then - Verify scan was inserted
        val retrievedScan = database.scanDao().getScanById(scanEntity.id)
        assertNotNull(retrievedScan, "Scan should be retrievable from database")
        assertEquals(scanEntity.id, retrievedScan.id, "Scan ID should match")
        assertEquals(scanEntity.name, retrievedScan.name, "Scan name should match")
        assertEquals(scanEntity.vertexCount, retrievedScan.vertexCount, "Vertex count should match")
        
        // When - Update scan
        val updatedScan = scanEntity.copy(name = "Updated Test Scan", isFavorite = true)
        database.scanDao().updateScan(updatedScan)
        
        // Then - Verify scan was updated
        val retrievedUpdatedScan = database.scanDao().getScanById(scanEntity.id)
        assertNotNull(retrievedUpdatedScan, "Updated scan should be retrievable")
        assertEquals("Updated Test Scan", retrievedUpdatedScan.name, "Scan name should be updated")
        assertTrue(retrievedUpdatedScan.isFavorite, "Scan should be marked as favorite")
        
        // When - Delete scan
        database.scanDao().deleteScanById(scanEntity.id)
        
        // Then - Verify scan was deleted
        val deletedScan = database.scanDao().getScanById(scanEntity.id)
        assertEquals(null, deletedScan, "Scan should be deleted from database")
    }
    
    @Test
    fun testMeshOptimizationAndStorageIntegration() = runTest {
        // Given - Create a complex test mesh
        val originalMesh = createComplexTestMesh()
        val scanId = "optimization_test_${System.currentTimeMillis()}"
        
        // When - Optimize mesh
        val optimizedMesh = meshOptimizer.simplifyMesh(originalMesh, targetRatio = 0.5f)
        
        // Then - Verify optimization
        assertTrue(optimizedMesh.triangleCount <= originalMesh.triangleCount,
            "Optimized mesh should have fewer triangles")
        
        // When - Save both original and optimized meshes
        val originalPath = storageManager.saveMesh("${scanId}_original", originalMesh, compress = false)
        val optimizedPath = storageManager.saveMesh("${scanId}_optimized", optimizedMesh, compress = true)
        
        // Then - Verify both were saved
        assertNotNull(originalPath, "Original mesh should be saved")
        assertNotNull(optimizedPath, "Optimized mesh should be saved")
        
        // When - Load both meshes
        val loadedOriginal = storageManager.loadMesh(originalPath)
        val loadedOptimized = storageManager.loadMesh(optimizedPath)
        
        // Then - Verify loaded meshes
        assertEquals(originalMesh.triangleCount, loadedOriginal.triangleCount,
            "Loaded original should match original")
        assertEquals(optimizedMesh.triangleCount, loadedOptimized.triangleCount,
            "Loaded optimized should match optimized")
        
        // Cleanup
        storageManager.deleteMesh(originalPath)
        storageManager.deleteMesh(optimizedPath)
    }
    
    @Test
    fun testMemoryManagementWithLargeMeshes() = runTest {
        // Given - Create multiple large meshes
        val meshes = mutableListOf<Mesh3D>()
        val scanIds = mutableListOf<String>()
        
        for (i in 1..5) {
            val mesh = createLargeTestMesh(vertexCount = 1000)
            val scanId = "large_mesh_$i"
            meshes.add(mesh)
            scanIds.add(scanId)
            
            // Cache mesh in memory manager
            memoryManager.cacheMesh(scanId, mesh)
        }
        
        // When - Check memory stats
        val memoryStats = memoryManager.getMemoryStats()
        
        // Then - Verify memory tracking
        assertTrue(memoryStats.meshCacheSize > 0, "Mesh cache should contain data")
        assertTrue(memoryStats.appMemoryUsage > 0, "App should be using memory")
        
        // When - Clear cache
        val freedBytes = memoryManager.clearCache(MemoryManager.CacheType.MESH)
        
        // Then - Verify cache was cleared
        assertTrue(freedBytes > 0, "Should have freed some memory")
        
        val clearedStats = memoryManager.getMemoryStats()
        assertTrue(clearedStats.meshCacheSize < memoryStats.meshCacheSize,
            "Mesh cache size should be reduced")
        
        // When - Try to retrieve cached meshes (should be null after clearing)
        for (scanId in scanIds) {
            val cachedMesh = memoryManager.getCachedMesh(scanId)
            assertEquals(null, cachedMesh, "Cached mesh should be null after clearing")
        }
    }
    
    @Test
    fun testStorageSpaceManagement() = runTest {
        // Given - Get initial storage info
        val initialStorageInfo = storageManager.getStorageInfo()
        
        // When - Save multiple meshes
        val savedPaths = mutableListOf<String>()
        for (i in 1..3) {
            val mesh = createTestMesh()
            val scanId = "storage_test_$i"
            val path = storageManager.saveMesh(scanId, mesh, compress = true)
            savedPaths.add(path)
        }
        
        // Then - Verify storage usage increased
        val afterSaveStorageInfo = storageManager.getStorageInfo()
        assertTrue(afterSaveStorageInfo.scansSize > initialStorageInfo.scansSize,
            "Storage usage should increase after saving meshes")
        
        // When - Delete saved meshes
        for (path in savedPaths) {
            storageManager.deleteMesh(path)
        }
        
        // Then - Verify storage usage decreased
        val afterDeleteStorageInfo = storageManager.getStorageInfo()
        assertTrue(afterDeleteStorageInfo.scansSize <= afterSaveStorageInfo.scansSize,
            "Storage usage should decrease after deleting meshes")
    }
    
    @Test
    fun testDatabaseQueryPerformance() = runTest {
        // Given - Insert multiple scan entities
        val scanEntities = mutableListOf<ScanEntity>()
        for (i in 1..100) {
            val scanEntity = ScanEntity(
                id = "perf_test_$i",
                name = "Performance Test Scan $i",
                filePath = "/test/path/scan_$i.s3d",
                thumbnailPath = null,
                createdAt = Date(System.currentTimeMillis() - i * 1000L),
                modifiedAt = Date(),
                fileSize = (i * 1024).toLong(),
                vertexCount = i * 10,
                triangleCount = i * 5,
                quality = Mesh3D.MeshQuality.values()[i % 3],
                hasTexture = i % 2 == 0,
                hasColors = i % 3 == 0,
                scanDuration = (i * 100).toLong(),
                boundingBoxMinX = -1f,
                boundingBoxMinY = -1f,
                boundingBoxMinZ = -1f,
                boundingBoxMaxX = 1f,
                boundingBoxMaxY = 1f,
                boundingBoxMaxZ = 1f,
                isFavorite = i % 5 == 0,
                isCloudSynced = i % 7 == 0
            )
            scanEntities.add(scanEntity)
        }
        
        // When - Insert all entities
        val insertStartTime = System.currentTimeMillis()
        database.scanDao().insertScans(scanEntities)
        val insertEndTime = System.currentTimeMillis()
        
        // Then - Verify insertion performance
        val insertDuration = insertEndTime - insertStartTime
        assertTrue(insertDuration < 5000, "Bulk insert should complete within 5 seconds")
        
        // When - Query all scans
        val queryStartTime = System.currentTimeMillis()
        val allScans = database.scanDao().getAllScans().first()
        val queryEndTime = System.currentTimeMillis()
        
        // Then - Verify query performance and results
        val queryDuration = queryEndTime - queryStartTime
        assertTrue(queryDuration < 1000, "Query should complete within 1 second")
        assertEquals(100, allScans.size, "Should retrieve all inserted scans")
        
        // When - Query favorites
        val favoritesStartTime = System.currentTimeMillis()
        val favoriteScans = database.scanDao().getFavoriteScans().first()
        val favoritesEndTime = System.currentTimeMillis()
        
        // Then - Verify favorites query
        val favoritesDuration = favoritesEndTime - favoritesStartTime
        assertTrue(favoritesDuration < 1000, "Favorites query should complete within 1 second")
        assertEquals(20, favoriteScans.size, "Should retrieve correct number of favorites")
        
        // Cleanup
        database.scanDao().deleteAllScans()
    }
    
    private fun createTestMesh(): Mesh3D {
        val vertices = floatArrayOf(
            0f, 0f, 0f,  // 0
            1f, 0f, 0f,  // 1
            0f, 1f, 0f,  // 2
            1f, 1f, 0f   // 3
        )
        
        val indices = intArrayOf(
            0, 1, 2,  // Triangle 1
            1, 3, 2   // Triangle 2
        )
        
        return Mesh3D(
            vertices = vertices,
            indices = indices,
            normals = null,
            textureCoordinates = null,
            colors = null,
            vertexCount = 4,
            triangleCount = 2,
            boundingBox = Mesh3D.BoundingBox(0f, 0f, 0f, 1f, 1f, 0f),
            metadata = Mesh3D.MeshMetadata(
                createdAt = System.currentTimeMillis(),
                scanDuration = 1000L,
                quality = Mesh3D.MeshQuality.HIGH,
                hasTexture = false,
                hasColors = false,
                estimatedFileSize = 1024L,
                scannerVersion = "1.0"
            )
        )
    }
    
    private fun createComplexTestMesh(): Mesh3D {
        // Create a more complex mesh with 20 vertices and 36 triangles
        val vertices = FloatArray(60) // 20 vertices * 3 coordinates
        val indices = IntArray(108) // 36 triangles * 3 indices
        
        // Generate vertices in a grid pattern
        var vertexIndex = 0
        for (y in 0..4) {
            for (x in 0..3) {
                vertices[vertexIndex * 3] = x.toFloat()
                vertices[vertexIndex * 3 + 1] = y.toFloat()
                vertices[vertexIndex * 3 + 2] = 0f
                vertexIndex++
            }
        }
        
        // Generate triangles
        var triangleIndex = 0
        for (y in 0..3) {
            for (x in 0..2) {
                val bottomLeft = y * 4 + x
                val bottomRight = bottomLeft + 1
                val topLeft = bottomLeft + 4
                val topRight = topLeft + 1
                
                // First triangle
                indices[triangleIndex * 3] = bottomLeft
                indices[triangleIndex * 3 + 1] = bottomRight
                indices[triangleIndex * 3 + 2] = topLeft
                triangleIndex++
                
                // Second triangle
                indices[triangleIndex * 3] = bottomRight
                indices[triangleIndex * 3 + 1] = topRight
                indices[triangleIndex * 3 + 2] = topLeft
                triangleIndex++
            }
        }
        
        return Mesh3D(
            vertices = vertices,
            indices = indices,
            normals = null,
            textureCoordinates = null,
            colors = null,
            vertexCount = 20,
            triangleCount = 24,
            boundingBox = Mesh3D.BoundingBox(0f, 0f, 0f, 3f, 4f, 0f),
            metadata = Mesh3D.MeshMetadata(
                createdAt = System.currentTimeMillis(),
                scanDuration = 2000L,
                quality = Mesh3D.MeshQuality.HIGH,
                hasTexture = false,
                hasColors = false,
                estimatedFileSize = 2048L,
                scannerVersion = "1.0"
            )
        )
    }
    
    private fun createLargeTestMesh(vertexCount: Int): Mesh3D {
        val vertices = FloatArray(vertexCount * 3)
        val triangleCount = (vertexCount - 2) // Simple fan triangulation
        val indices = IntArray(triangleCount * 3)
        
        // Generate vertices in a circle
        for (i in 0 until vertexCount) {
            val angle = (i.toFloat() / vertexCount) * 2 * Math.PI
            vertices[i * 3] = kotlin.math.cos(angle).toFloat()
            vertices[i * 3 + 1] = kotlin.math.sin(angle).toFloat()
            vertices[i * 3 + 2] = 0f
        }
        
        // Generate triangles using fan triangulation
        for (i in 0 until triangleCount) {
            indices[i * 3] = 0
            indices[i * 3 + 1] = i + 1
            indices[i * 3 + 2] = i + 2
        }
        
        return Mesh3D(
            vertices = vertices,
            indices = indices,
            normals = null,
            textureCoordinates = null,
            colors = null,
            vertexCount = vertexCount,
            triangleCount = triangleCount,
            boundingBox = Mesh3D.BoundingBox(-1f, -1f, 0f, 1f, 1f, 0f),
            metadata = Mesh3D.MeshMetadata(
                createdAt = System.currentTimeMillis(),
                scanDuration = 3000L,
                quality = Mesh3D.MeshQuality.MEDIUM,
                hasTexture = false,
                hasColors = false,
                estimatedFileSize = (vertexCount * 12 + triangleCount * 12).toLong(),
                scannerVersion = "1.0"
            )
        )
    }
}

