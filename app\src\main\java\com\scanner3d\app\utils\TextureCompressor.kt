package com.scanner3d.app.utils

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream
import kotlin.math.*

class TextureCompressor {
    
    companion object {
        private const val TAG = "TextureCompressor"
        private const val DEFAULT_QUALITY = 85
        private const val MAX_TEXTURE_SIZE = 2048
        private const val MIN_TEXTURE_SIZE = 256
    }
    
    enum class CompressionFormat {
        JPEG,
        PNG,
        WEBP,
        ETC2,
        ASTC
    }
    
    enum class CompressionQuality {
        LOW(60),
        MEDIUM(75),
        HIGH(85),
        LOSSLESS(100);
        
        val value: Int
        
        constructor(value: Int) {
            this.value = value
        }
    }
    
    data class CompressionResult(
        val compressedTexture: ByteArray,
        val originalSize: Int,
        val compressedSize: Int,
        val compressionRatio: Float,
        val format: CompressionFormat,
        val quality: CompressionQuality,
        val dimensions: Pair<Int, Int>
    ) {
        val spaceSaved: Int
            get() = originalSize - compressedSize
        
        val spaceSavedPercentage: Float
            get() = (spaceSaved.toFloat() / originalSize.toFloat()) * 100f
    }
    
    suspend fun compressTexture(
        bitmap: Bitmap,
        format: CompressionFormat = CompressionFormat.JPEG,
        quality: CompressionQuality = CompressionQuality.MEDIUM,
        maxSize: Int = MAX_TEXTURE_SIZE
    ): CompressionResult = withContext(Dispatchers.Default) {
        
        Log.d(TAG, "Compressing texture: ${bitmap.width}x${bitmap.height} -> format: $format, quality: $quality")
        
        try {
            // Step 1: Resize if necessary
            val resizedBitmap = resizeTexture(bitmap, maxSize)
            
            // Step 2: Optimize texture layout
            val optimizedBitmap = optimizeTextureLayout(resizedBitmap)
            
            // Step 3: Apply compression
            val compressedData = when (format) {
                CompressionFormat.JPEG -> compressToJPEG(optimizedBitmap, quality)
                CompressionFormat.PNG -> compressToPNG(optimizedBitmap)
                CompressionFormat.WEBP -> compressToWebP(optimizedBitmap, quality)
                CompressionFormat.ETC2 -> compressToETC2(optimizedBitmap)
                CompressionFormat.ASTC -> compressToASTC(optimizedBitmap)
            }
            
            val originalSize = bitmap.byteCount
            val compressedSize = compressedData.size
            val compressionRatio = compressedSize.toFloat() / originalSize.toFloat()
            
            Log.d(TAG, "Texture compression completed: $originalSize -> $compressedSize bytes (${String.format("%.1f", compressionRatio * 100)}%)")
            
            CompressionResult(
                compressedTexture = compressedData,
                originalSize = originalSize,
                compressedSize = compressedSize,
                compressionRatio = compressionRatio,
                format = format,
                quality = quality,
                dimensions = Pair(optimizedBitmap.width, optimizedBitmap.height)
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Texture compression failed", e)
            throw CompressionException("Texture compression failed: ${e.message}", e)
        }
    }
    
    suspend fun generateMipmaps(
        bitmap: Bitmap,
        levels: Int = 4
    ): List<Bitmap> = withContext(Dispatchers.Default) {
        
        val mipmaps = mutableListOf<Bitmap>()
        var currentBitmap = bitmap
        
        for (level in 0 until levels) {
            mipmaps.add(currentBitmap.copy(currentBitmap.config, false))
            
            if (level < levels - 1) {
                val newWidth = maxOf(currentBitmap.width / 2, 1)
                val newHeight = maxOf(currentBitmap.height / 2, 1)
                
                currentBitmap = Bitmap.createScaledBitmap(
                    currentBitmap,
                    newWidth,
                    newHeight,
                    true
                )
            }
        }
        
        Log.d(TAG, "Generated ${mipmaps.size} mipmap levels")
        mipmaps
    }
    
    suspend fun createTextureAtlas(
        textures: List<Bitmap>,
        maxAtlasSize: Int = MAX_TEXTURE_SIZE
    ): TextureAtlas = withContext(Dispatchers.Default) {
        
        Log.d(TAG, "Creating texture atlas from ${textures.size} textures")
        
        // Sort textures by area (largest first) for better packing
        val sortedTextures = textures.mapIndexed { index, bitmap ->
            TextureInfo(index, bitmap, bitmap.width * bitmap.height)
        }.sortedByDescending { it.area }
        
        // Pack textures using bin packing algorithm
        val packedTextures = packTextures(sortedTextures, maxAtlasSize)
        
        // Create atlas bitmap
        val atlasSize = calculateOptimalAtlasSize(packedTextures, maxAtlasSize)
        val atlasBitmap = Bitmap.createBitmap(atlasSize, atlasSize, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(atlasBitmap)
        
        val uvMappings = mutableMapOf<Int, UVMapping>()
        
        // Draw textures to atlas
        for (packedTexture in packedTextures) {
            canvas.drawBitmap(
                packedTexture.bitmap,
                null,
                Rect(packedTexture.x, packedTexture.y, 
                     packedTexture.x + packedTexture.width, 
                     packedTexture.y + packedTexture.height),
                null
            )
            
            // Calculate UV coordinates
            val uvMapping = UVMapping(
                u1 = packedTexture.x.toFloat() / atlasSize,
                v1 = packedTexture.y.toFloat() / atlasSize,
                u2 = (packedTexture.x + packedTexture.width).toFloat() / atlasSize,
                v2 = (packedTexture.y + packedTexture.height).toFloat() / atlasSize
            )
            
            uvMappings[packedTexture.originalIndex] = uvMapping
        }
        
        Log.d(TAG, "Texture atlas created: ${atlasSize}x${atlasSize}")
        
        TextureAtlas(
            atlasBitmap = atlasBitmap,
            uvMappings = uvMappings,
            atlasSize = atlasSize,
            textureCount = textures.size
        )
    }
    
    private fun resizeTexture(bitmap: Bitmap, maxSize: Int): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        
        if (width <= maxSize && height <= maxSize) {
            return bitmap
        }
        
        val scale = minOf(maxSize.toFloat() / width, maxSize.toFloat() / height)
        val newWidth = (width * scale).toInt()
        val newHeight = (height * scale).toInt()
        
        // Ensure dimensions are power of 2 for better GPU performance
        val finalWidth = nextPowerOfTwo(newWidth)
        val finalHeight = nextPowerOfTwo(newHeight)
        
        return Bitmap.createScaledBitmap(bitmap, finalWidth, finalHeight, true)
    }
    
    private fun optimizeTextureLayout(bitmap: Bitmap): Bitmap {
        // Apply texture optimization techniques
        
        // 1. Remove alpha channel if not needed
        val hasAlpha = bitmap.hasAlpha()
        if (!hasAlpha && bitmap.config == Bitmap.Config.ARGB_8888) {
            val optimizedBitmap = bitmap.copy(Bitmap.Config.RGB_565, false)
            return optimizedBitmap
        }
        
        // 2. Apply dithering for better quality at lower bit depths
        return applyDithering(bitmap)
    }
    
    private fun applyDithering(bitmap: Bitmap): Bitmap {
        // Floyd-Steinberg dithering implementation
        val width = bitmap.width
        val height = bitmap.height
        val pixels = IntArray(width * height)
        bitmap.getPixels(pixels, 0, width, 0, 0, width, height)
        
        for (y in 0 until height) {
            for (x in 0 until width) {
                val index = y * width + x
                val pixel = pixels[index]
                
                // Extract RGB components
                val r = (pixel shr 16) and 0xFF
                val g = (pixel shr 8) and 0xFF
                val b = pixel and 0xFF
                
                // Quantize to lower bit depth
                val newR = (r / 32) * 32
                val newG = (g / 32) * 32
                val newB = (b / 32) * 32
                
                // Calculate error
                val errorR = r - newR
                val errorG = g - newG
                val errorB = b - newB
                
                // Set new pixel value
                pixels[index] = (0xFF shl 24) or (newR shl 16) or (newG shl 8) or newB
                
                // Distribute error to neighboring pixels
                distributeError(pixels, width, height, x, y, errorR, errorG, errorB)
            }
        }
        
        val ditheredBitmap = Bitmap.createBitmap(width, height, bitmap.config)
        ditheredBitmap.setPixels(pixels, 0, width, 0, 0, width, height)
        return ditheredBitmap
    }
    
    private fun distributeError(
        pixels: IntArray, width: Int, height: Int,
        x: Int, y: Int, errorR: Int, errorG: Int, errorB: Int
    ) {
        // Floyd-Steinberg error distribution pattern
        val errorDistribution = arrayOf(
            Pair(1, 0) to 7.0/16.0,  // Right
            Pair(-1, 1) to 3.0/16.0, // Bottom-left
            Pair(0, 1) to 5.0/16.0,  // Bottom
            Pair(1, 1) to 1.0/16.0   // Bottom-right
        )
        
        for ((offset, weight) in errorDistribution) {
            val nx = x + offset.first
            val ny = y + offset.second
            
            if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
                val index = ny * width + nx
                val pixel = pixels[index]
                
                val r = ((pixel shr 16) and 0xFF) + (errorR * weight).toInt()
                val g = ((pixel shr 8) and 0xFF) + (errorG * weight).toInt()
                val b = (pixel and 0xFF) + (errorB * weight).toInt()
                
                val clampedR = r.coerceIn(0, 255)
                val clampedG = g.coerceIn(0, 255)
                val clampedB = b.coerceIn(0, 255)
                
                pixels[index] = (0xFF shl 24) or (clampedR shl 16) or (clampedG shl 8) or clampedB
            }
        }
    }
    
    private fun compressToJPEG(bitmap: Bitmap, quality: CompressionQuality): ByteArray {
        val outputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.JPEG, quality.value, outputStream)
        return outputStream.toByteArray()
    }
    
    private fun compressToPNG(bitmap: Bitmap): ByteArray {
        val outputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
        return outputStream.toByteArray()
    }
    
    private fun compressToWebP(bitmap: Bitmap, quality: CompressionQuality): ByteArray {
        val outputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.WEBP, quality.value, outputStream)
        return outputStream.toByteArray()
    }
    
    private fun compressToETC2(bitmap: Bitmap): ByteArray {
        // ETC2 compression would require native implementation or external library
        // For now, fall back to JPEG
        return compressToJPEG(bitmap, CompressionQuality.HIGH)
    }
    
    private fun compressToASTC(bitmap: Bitmap): ByteArray {
        // ASTC compression would require native implementation or external library
        // For now, fall back to JPEG
        return compressToJPEG(bitmap, CompressionQuality.HIGH)
    }
    
    private fun packTextures(textures: List<TextureInfo>, maxSize: Int): List<PackedTexture> {
        val packedTextures = mutableListOf<PackedTexture>()
        val bins = mutableListOf<Bin>()
        
        for (texture in textures) {
            var placed = false
            
            // Try to place in existing bins
            for (bin in bins) {
                val position = bin.findSpace(texture.bitmap.width, texture.bitmap.height)
                if (position != null) {
                    packedTextures.add(
                        PackedTexture(
                            originalIndex = texture.originalIndex,
                            bitmap = texture.bitmap,
                            x = position.first,
                            y = position.second,
                            width = texture.bitmap.width,
                            height = texture.bitmap.height
                        )
                    )
                    bin.occupy(position.first, position.second, texture.bitmap.width, texture.bitmap.height)
                    placed = true
                    break
                }
            }
            
            // Create new bin if needed
            if (!placed) {
                val newBin = Bin(maxSize, maxSize)
                val position = newBin.findSpace(texture.bitmap.width, texture.bitmap.height)
                if (position != null) {
                    packedTextures.add(
                        PackedTexture(
                            originalIndex = texture.originalIndex,
                            bitmap = texture.bitmap,
                            x = position.first,
                            y = position.second,
                            width = texture.bitmap.width,
                            height = texture.bitmap.height
                        )
                    )
                    newBin.occupy(position.first, position.second, texture.bitmap.width, texture.bitmap.height)
                    bins.add(newBin)
                }
            }
        }
        
        return packedTextures
    }
    
    private fun calculateOptimalAtlasSize(packedTextures: List<PackedTexture>, maxSize: Int): Int {
        var requiredWidth = 0
        var requiredHeight = 0
        
        for (texture in packedTextures) {
            requiredWidth = maxOf(requiredWidth, texture.x + texture.width)
            requiredHeight = maxOf(requiredHeight, texture.y + texture.height)
        }
        
        val size = maxOf(requiredWidth, requiredHeight)
        return nextPowerOfTwo(size).coerceAtMost(maxSize)
    }
    
    private fun nextPowerOfTwo(value: Int): Int {
        var power = 1
        while (power < value) {
            power *= 2
        }
        return power
    }
    
    // Data classes for texture packing
    private data class TextureInfo(
        val originalIndex: Int,
        val bitmap: Bitmap,
        val area: Int
    )
    
    private data class PackedTexture(
        val originalIndex: Int,
        val bitmap: Bitmap,
        val x: Int,
        val y: Int,
        val width: Int,
        val height: Int
    )
    
    private class Bin(private val width: Int, private val height: Int) {
        private val occupied = Array(height) { BooleanArray(width) }
        
        fun findSpace(reqWidth: Int, reqHeight: Int): Pair<Int, Int>? {
            for (y in 0..height - reqHeight) {
                for (x in 0..width - reqWidth) {
                    if (canFit(x, y, reqWidth, reqHeight)) {
                        return Pair(x, y)
                    }
                }
            }
            return null
        }
        
        private fun canFit(x: Int, y: Int, reqWidth: Int, reqHeight: Int): Boolean {
            for (dy in 0 until reqHeight) {
                for (dx in 0 until reqWidth) {
                    if (occupied[y + dy][x + dx]) {
                        return false
                    }
                }
            }
            return true
        }
        
        fun occupy(x: Int, y: Int, reqWidth: Int, reqHeight: Int) {
            for (dy in 0 until reqHeight) {
                for (dx in 0 until reqWidth) {
                    occupied[y + dy][x + dx] = true
                }
            }
        }
    }
    
    data class UVMapping(
        val u1: Float,
        val v1: Float,
        val u2: Float,
        val v2: Float
    )
    
    data class TextureAtlas(
        val atlasBitmap: Bitmap,
        val uvMappings: Map<Int, UVMapping>,
        val atlasSize: Int,
        val textureCount: Int
    )
    
    class CompressionException(message: String, cause: Throwable? = null) : Exception(message, cause)
}

