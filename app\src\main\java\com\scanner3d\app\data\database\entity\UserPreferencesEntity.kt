package com.scanner3d.app.data.database.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.scanner3d.app.data.model.Mesh3D

@Entity(tableName = "user_preferences")
data class UserPreferencesEntity(
    @PrimaryKey
    val id: Int = 1, // Single row for user preferences
    
    // Scanning preferences
    val defaultScanQuality: Mesh3D.MeshQuality = Mesh3D.MeshQuality.MEDIUM,
    val autoSaveEnabled: Boolean = true,
    val autoSaveInterval: Int = 30, // seconds
    val maxScanDuration: Int = 300, // seconds
    val enableDepthFiltering: Boolean = true,
    val enableMeshSmoothing: Boolean = true,
    val textureResolution: Int = 1024,
    
    // Storage preferences
    val maxLocalStorageGB: Float = 1.0f,
    val compressionEnabled: Boolean = true,
    val compressionLevel: Int = 5, // 1-9
    val autoCleanupEnabled: Boolean = true,
    val cleanupThresholdDays: Int = 30,
    
    // Cloud sync preferences
    val cloudSyncEnabled: Boolean = false,
    val autoSyncEnabled: Boolean = true,
    val syncOnWifiOnly: Boolean = true,
    val syncQuality: Mesh3D.MeshQuality = Mesh3D.MeshQuality.MEDIUM,
    val encryptionEnabled: Boolean = true,
    
    // UI preferences
    val defaultViewMode: String = "grid", // "grid" or "list"
    val showThumbnails: Boolean = true,
    val animationsEnabled: Boolean = true,
    val hapticFeedbackEnabled: Boolean = true,
    val keepScreenOn: Boolean = true,
    
    // Export preferences
    val defaultExportFormat: String = "OBJ",
    val defaultExportQuality: Mesh3D.MeshQuality = Mesh3D.MeshQuality.HIGH,
    val includeTextures: Boolean = true,
    val includeColors: Boolean = true,
    val exportCompressionEnabled: Boolean = false,
    
    // Privacy preferences
    val analyticsEnabled: Boolean = true,
    val crashReportingEnabled: Boolean = true,
    val locationTaggingEnabled: Boolean = false,
    val shareUsageData: Boolean = false,
    
    // Performance preferences
    val maxRAMUsageGB: Float = 2.0f,
    val maxCacheSizeMB: Int = 500,
    val gpuAccelerationEnabled: Boolean = true,
    val backgroundProcessingEnabled: Boolean = true,
    val powerSavingMode: Boolean = false,
    
    // Notification preferences
    val scanCompleteNotifications: Boolean = true,
    val exportCompleteNotifications: Boolean = true,
    val syncCompleteNotifications: Boolean = false,
    val lowStorageNotifications: Boolean = true,
    val errorNotifications: Boolean = true,
    
    val lastModified: Long = System.currentTimeMillis()
)

