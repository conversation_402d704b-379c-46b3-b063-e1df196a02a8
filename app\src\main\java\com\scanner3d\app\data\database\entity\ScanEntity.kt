package com.scanner3d.app.data.database.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.scanner3d.app.data.model.Mesh3D
import java.util.Date

@Entity(tableName = "scans")
data class ScanEntity(
    @PrimaryKey
    val id: String,
    val name: String,
    val filePath: String,
    val thumbnailPath: String?,
    val createdAt: Date,
    val modifiedAt: Date,
    val fileSize: Long,
    val vertexCount: Int,
    val triangleCount: Int,
    val quality: Mesh3D.MeshQuality,
    val hasTexture: Boolean,
    val hasColors: Boolean,
    val scanDuration: Long,
    val boundingBoxMinX: Float,
    val boundingBoxMinY: Float,
    val boundingBoxMinZ: Float,
    val boundingBoxMaxX: Float,
    val boundingBoxMaxY: Float,
    val boundingBoxMaxZ: Float,
    val isFavorite: Boolean = false,
    val isCloudSynced: Boolean = false,
    val cloudPath: String? = null,
    val tags: List<String> = emptyList(),
    val notes: String? = null,
    val exportFormats: List<String> = emptyList(),
    val compressionRatio: Float = 1.0f,
    val scannerVersion: String = "1.0"
)

