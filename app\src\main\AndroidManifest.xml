<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Required permissions -->
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    <uses-permission android:name="android.permission.USE_FINGERPRINT" />

    <!-- Camera features -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.camera.flash"
        android:required="false" />

    <!-- ARCore features -->
    <uses-feature
        android:name="android.hardware.camera.ar"
        android:required="true" />

    <!-- OpenGL ES 3.0 -->
    <uses-feature
        android:glEsVersion="0x00030000"
        android:required="true" />

    <!-- Sensors -->
    <uses-feature
        android:name="android.hardware.sensor.accelerometer"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.sensor.gyroscope"
        android:required="true" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Scanner3D"
        android:hardwareAccelerated="true"
        tools:targetApi="31">

        <!-- ARCore metadata -->
        <meta-data
            android:name="com.google.ar.core"
            android:value="required" />

        <!-- Main Activity -->
        <activity
            android:name=".ui.MainActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Scanner3D.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Scanning Activity -->
        <activity
            android:name=".ui.scanning.ScanningActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Scanner3D.NoActionBar" />

        <!-- Model Viewer Activity -->
        <activity
            android:name=".ui.model.ModelViewerActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Scanner3D.NoActionBar" />

        <!-- Gallery Activity -->
        <activity
            android:name=".ui.gallery.GalleryActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Scanner3D.NoActionBar" />

        <!-- Authentication Activity -->
        <activity
            android:name=".ui.auth.AuthenticationActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.Scanner3D.NoActionBar" />

        <!-- File Provider for sharing files -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

    </application>

</manifest>

