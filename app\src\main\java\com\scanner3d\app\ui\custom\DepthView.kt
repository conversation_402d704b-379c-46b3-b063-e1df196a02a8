package com.scanner3d.app.ui.custom

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import kotlin.math.*

class DepthView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    
    private var depthData: FloatArray? = null
    private var depthWidth = 0
    private var depthHeight = 0
    
    private val paint = Paint().apply {
        isAntiAlias = true
    }
    
    private val overlayPaint = Paint().apply {
        color = Color.WHITE
        textSize = 48f
        textAlign = Paint.Align.CENTER
        isAntiAlias = true
    }
    
    private var depthBitmap: Bitmap? = null
    private val matrix = Matrix()
    
    fun updateDepthData(data: FloatArray, width: Int, height: Int) {
        depthData = data
        depthWidth = width
        depthHeight = height
        
        // Create bitmap from depth data
        createDepthBitmap()
        invalidate()
    }
    
    private fun createDepthBitmap() {
        val data = depthData ?: return
        if (depthWidth <= 0 || depthHeight <= 0) return
        
        // Create bitmap to visualize depth data
        val bitmap = Bitmap.createBitmap(depthWidth, depthHeight, Bitmap.Config.ARGB_8888)
        
        // Find min and max depth values for normalization
        var minDepth = Float.MAX_VALUE
        var maxDepth = Float.MIN_VALUE
        
        for (depth in data) {
            if (depth > 0) { // Ignore invalid depth values
                minDepth = min(minDepth, depth)
                maxDepth = max(maxDepth, depth)
            }
        }
        
        val depthRange = maxDepth - minDepth
        
        // Convert depth values to colors
        val pixels = IntArray(depthWidth * depthHeight)
        
        for (y in 0 until depthHeight) {
            for (x in 0 until depthWidth) {
                val index = y * depthWidth + x
                val depth = data[index]
                
                val color = if (depth <= 0) {
                    Color.BLACK // Invalid depth
                } else {
                    // Normalize depth to 0-1 range
                    val normalizedDepth = if (depthRange > 0) {
                        (depth - minDepth) / depthRange
                    } else {
                        0.5f
                    }
                    
                    // Create color based on depth (closer = warmer colors)
                    depthToColor(normalizedDepth)
                }
                
                pixels[index] = color
            }
        }
        
        bitmap.setPixels(pixels, 0, depthWidth, 0, 0, depthWidth, depthHeight)
        depthBitmap = bitmap
    }
    
    private fun depthToColor(normalizedDepth: Float): Int {
        // Create a color map from blue (far) to red (near)
        val hue = (1f - normalizedDepth) * 240f // Blue to Red
        val saturation = 1f
        val value = 0.8f + normalizedDepth * 0.2f // Slightly brighter for closer objects
        
        return Color.HSVToColor(floatArrayOf(hue, saturation, value))
    }
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        val bitmap = depthBitmap
        if (bitmap != null) {
            // Scale bitmap to fit view
            val scaleX = width.toFloat() / bitmap.width
            val scaleY = height.toFloat() / bitmap.height
            val scale = min(scaleX, scaleY)
            
            matrix.reset()
            matrix.setScale(scale, scale)
            
            // Center the bitmap
            val scaledWidth = bitmap.width * scale
            val scaledHeight = bitmap.height * scale
            val offsetX = (width - scaledWidth) / 2
            val offsetY = (height - scaledHeight) / 2
            
            matrix.postTranslate(offsetX, offsetY)
            
            canvas.drawBitmap(bitmap, matrix, paint)
            
            // Draw depth info overlay
            drawDepthInfo(canvas)
        } else {
            // Draw placeholder
            canvas.drawColor(Color.DKGRAY)
            canvas.drawText(
                "Depth View",
                width / 2f,
                height / 2f,
                overlayPaint
            )
        }
    }
    
    private fun drawDepthInfo(canvas: Canvas) {
        val data = depthData ?: return
        
        // Calculate some statistics
        var validPoints = 0
        var totalDepth = 0f
        var minDepth = Float.MAX_VALUE
        var maxDepth = Float.MIN_VALUE
        
        for (depth in data) {
            if (depth > 0) {
                validPoints++
                totalDepth += depth
                minDepth = min(minDepth, depth)
                maxDepth = max(maxDepth, depth)
            }
        }
        
        if (validPoints > 0) {
            val avgDepth = totalDepth / validPoints
            
            // Draw info text
            val textPaint = Paint().apply {
                color = Color.WHITE
                textSize = 32f
                isAntiAlias = true
                setShadowLayer(4f, 2f, 2f, Color.BLACK)
            }
            
            val infoText = listOf(
                "Valid Points: $validPoints",
                "Avg Depth: ${String.format("%.2f", avgDepth)}m",
                "Range: ${String.format("%.2f", minDepth)}m - ${String.format("%.2f", maxDepth)}m"
            )
            
            var y = 60f
            for (text in infoText) {
                canvas.drawText(text, 20f, y, textPaint)
                y += 40f
            }
        }
        
        // Draw color scale legend
        drawColorScale(canvas)
    }
    
    private fun drawColorScale(canvas: Canvas) {
        val legendWidth = 200f
        val legendHeight = 20f
        val legendX = width - legendWidth - 20f
        val legendY = height - legendHeight - 60f
        
        // Draw color gradient
        val gradient = LinearGradient(
            legendX, legendY,
            legendX + legendWidth, legendY,
            intArrayOf(
                Color.HSVToColor(floatArrayOf(240f, 1f, 0.8f)), // Blue (far)
                Color.HSVToColor(floatArrayOf(120f, 1f, 0.8f)), // Green (medium)
                Color.HSVToColor(floatArrayOf(0f, 1f, 0.8f))     // Red (near)
            ),
            null,
            Shader.TileMode.CLAMP
        )
        
        val gradientPaint = Paint().apply {
            shader = gradient
        }
        
        canvas.drawRect(legendX, legendY, legendX + legendWidth, legendY + legendHeight, gradientPaint)
        
        // Draw labels
        val labelPaint = Paint().apply {
            color = Color.WHITE
            textSize = 24f
            isAntiAlias = true
            setShadowLayer(2f, 1f, 1f, Color.BLACK)
        }
        
        canvas.drawText("Far", legendX, legendY - 10f, labelPaint)
        canvas.drawText("Near", legendX + legendWidth - 40f, legendY - 10f, labelPaint)
    }
    
    fun clearDepthData() {
        depthData = null
        depthBitmap = null
        invalidate()
    }
}

